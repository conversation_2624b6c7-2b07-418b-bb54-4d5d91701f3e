using System;
using System.Collections.Generic;
using System.Configuration;
using System.Diagnostics;
using System.Linq;
using System.Transactions;
using ProManage.Modules.Connections;
using ProManage.Modules.Models;

namespace ProManage.Modules.Services
{
    /// <summary>
    /// Service for synchronizing forms with permission database tables
    /// Uses SERIALIZABLE isolation with configurable timeout
    /// </summary>
    public static class PermissionSyncService
    {
        /// <summary>
        /// Add missing forms to both permission tables
        /// </summary>
        /// <param name="formNames">List of form names to add</param>
        /// <param name="progress">Progress reporter (optional)</param>
        /// <returns>True if all forms added successfully</returns>
        public static bool AddMissingFormsToPermissions(List<string> formNames, IProgress<SyncProgress> progress = null)
        {
            if (formNames == null || formNames.Count == 0)
                return true;

            var startTime = DateTime.Now;
            var totalOperations = formNames.Count * 2; // Each form goes to both tables
            var completed = 0;

            try
            {
                SyncLoggingService.LogSyncStart(formNames.Count);

                using (var scope = new TransactionScope(
                    TransactionScopeOption.Required,
                    new TransactionOptions
                    {
                        IsolationLevel = IsolationLevel.Serializable,
                        Timeout = GetConfigurableTimeout()
                    }))
                {
                    foreach (var formName in formNames)
                    {
                        ReportProgress(progress, completed, totalOperations, $"Adding form to role permissions: {formName}");

                        // Add to role_permissions for all roles
                        if (!AddFormToRolePermissions(formName))
                        {
                            SyncLoggingService.LogSyncError($"AddFormToRolePermissions({formName})", 
                                new Exception("Failed to add form to role permissions"));
                            return false;
                        }
                        completed++;

                        ReportProgress(progress, completed, totalOperations, $"Adding form to user permissions: {formName}");

                        // Add to user_permissions for all users
                        if (!AddFormToUserPermissions(formName))
                        {
                            SyncLoggingService.LogSyncError($"AddFormToUserPermissions({formName})", 
                                new Exception("Failed to add form to user permissions"));
                            return false;
                        }
                        completed++;
                    }

                    scope.Complete();
                }

                var duration = DateTime.Now - startTime;
                SyncLoggingService.LogSyncComplete(formNames.Count, 0, duration);
                return true;
            }
            catch (Exception ex)
            {
                SyncLoggingService.LogSyncError("AddMissingFormsToPermissions", ex);
                return false;
            }
        }

        /// <summary>
        /// Remove obsolete forms from both permission tables
        /// </summary>
        /// <param name="formNames">List of form names to remove</param>
        /// <returns>True if all forms removed successfully</returns>
        public static bool RemoveObsoleteFormsFromPermissions(List<string> formNames)
        {
            if (formNames == null || formNames.Count == 0)
                return true;

            var startTime = DateTime.Now;

            try
            {
                SyncLoggingService.LogSyncStart(formNames.Count);

                using (var scope = new TransactionScope(
                    TransactionScopeOption.Required,
                    new TransactionOptions
                    {
                        IsolationLevel = IsolationLevel.Serializable,
                        Timeout = GetConfigurableTimeout()
                    }))
                {
                    foreach (var formName in formNames)
                    {
                        // Remove from role_permissions
                        if (!RemoveFormFromRolePermissions(formName))
                        {
                            SyncLoggingService.LogSyncError($"RemoveFormFromRolePermissions({formName})", 
                                new Exception("Failed to remove form from role permissions"));
                            return false;
                        }

                        // Remove from user_permissions
                        if (!RemoveFormFromUserPermissions(formName))
                        {
                            SyncLoggingService.LogSyncError($"RemoveFormFromUserPermissions({formName})", 
                                new Exception("Failed to remove form from user permissions"));
                            return false;
                        }
                    }

                    scope.Complete();
                }

                var duration = DateTime.Now - startTime;
                SyncLoggingService.LogSyncComplete(0, formNames.Count, duration);
                return true;
            }
            catch (Exception ex)
            {
                SyncLoggingService.LogSyncError("RemoveObsoleteFormsFromPermissions", ex);
                return false;
            }
        }

        /// <summary>
        /// Execute full synchronization with progress reporting
        /// </summary>
        /// <param name="progress">Progress reporter (optional)</param>
        /// <param name="timeout">Custom timeout (optional)</param>
        /// <returns>Sync result with details</returns>
        public static FormSyncResult ExecuteFullSync(IProgress<SyncProgress> progress = null, TimeSpan? timeout = null)
        {
            var result = new FormSyncResult
            {
                SyncTimestamp = DateTime.Now
            };

            var startTime = DateTime.Now;

            try
            {
                // Get forms comparison
                var comparison = FormDiscoveryService.CompareFormsWithDatabase();
                if (comparison == null)
                {
                    result.Errors.Add("Failed to compare forms with database");
                    return result;
                }

                result.HasMismatch = comparison.HasMismatch;
                result.MissingForms = comparison.MissingForms;
                result.ObsoleteForms = comparison.ObsoleteForms;
                result.ExistingForms = comparison.ExistingForms;

                if (!comparison.HasMismatch)
                {
                    result.SyncSuccess = true;
                    return result;
                }

                // Add missing forms
                if (comparison.MissingForms.Count > 0)
                {
                    if (!AddMissingFormsToPermissions(comparison.MissingForms, progress))
                    {
                        result.Errors.Add("Failed to add missing forms");
                        return result;
                    }
                }

                // Remove obsolete forms
                if (comparison.ObsoleteForms.Count > 0)
                {
                    if (!RemoveObsoleteFormsFromPermissions(comparison.ObsoleteForms))
                    {
                        result.Errors.Add("Failed to remove obsolete forms");
                        return result;
                    }
                }

                result.SyncSuccess = true;
                result.TotalFormsProcessed = comparison.MissingForms.Count + comparison.ObsoleteForms.Count;
            }
            catch (Exception ex)
            {
                result.Errors.Add($"Sync failed: {ex.Message}");
                SyncLoggingService.LogSyncError("ExecuteFullSync", ex);
            }
            finally
            {
                result.SyncDuration = DateTime.Now - startTime;
            }

            return result;
        }

        /// <summary>
        /// Validate permission tables integrity
        /// </summary>
        /// <returns>True if tables are valid</returns>
        public static bool ValidatePermissionTables()
        {
            try
            {
                // Check if tables exist and have expected structure
                var rolePermissionsCount = PermissionDatabaseService.GetAllRoles().Count;
                var userPermissionsCount = PermissionDatabaseService.GetAllUsers().Count;

                return rolePermissionsCount >= 0 && userPermissionsCount >= 0;
            }
            catch (Exception ex)
            {
                SyncLoggingService.LogSyncError("ValidatePermissionTables", ex);
                return false;
            }
        }

        /// <summary>
        /// Report progress to UI thread
        /// </summary>
        /// <param name="progress">Progress reporter</param>
        /// <param name="completed">Completed operations</param>
        /// <param name="total">Total operations</param>
        /// <param name="operation">Current operation description</param>
        public static void ReportProgress(IProgress<SyncProgress> progress, int completed, int total, string operation)
        {
            if (progress == null) return;

            try
            {
                var syncProgress = new SyncProgress
                {
                    TotalOperations = total,
                    CompletedOperations = completed,
                    CurrentOperation = operation,
                    StartTime = DateTime.Now
                };

                progress.Report(syncProgress);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error reporting progress: {ex.Message}");
            }
        }

        /// <summary>
        /// Get configurable timeout from app settings
        /// </summary>
        /// <returns>Timeout value</returns>
        public static TimeSpan GetConfigurableTimeout()
        {
            try
            {
                var timeoutMinutes = ConfigurationManager.AppSettings["FormSyncTimeoutMinutes"];
                if (int.TryParse(timeoutMinutes, out int minutes) && minutes > 0)
                {
                    return TimeSpan.FromMinutes(minutes);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error reading timeout configuration: {ex.Message}");
            }

            // Default to 5 minutes
            return TimeSpan.FromMinutes(5);
        }

        #region Private Helper Methods

        private static bool AddFormToRolePermissions(string formName)
        {
            try
            {
                var roles = PermissionDatabaseService.GetAllRoles();
                foreach (var role in roles)
                {
                    // Add form with default permissions (false for all)
                    PermissionDatabaseService.AddRolePermission(role.RoleId, formName, false, false, false, false, false);
                }
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error adding form to role permissions: {ex.Message}");
                return false;
            }
        }

        private static bool AddFormToUserPermissions(string formName)
        {
            try
            {
                var users = PermissionDatabaseService.GetAllUsers();
                foreach (var user in users)
                {
                    // Add form with null permissions (inherit from role)
                    PermissionDatabaseService.AddUserPermission(user.UserId, formName, null, null, null, null, null);
                }
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error adding form to user permissions: {ex.Message}");
                return false;
            }
        }

        private static bool RemoveFormFromRolePermissions(string formName)
        {
            try
            {
                PermissionDatabaseService.RemoveFormFromRolePermissions(formName);
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error removing form from role permissions: {ex.Message}");
                return false;
            }
        }

        private static bool RemoveFormFromUserPermissions(string formName)
        {
            try
            {
                PermissionDatabaseService.RemoveFormFromUserPermissions(formName);
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error removing form from user permissions: {ex.Message}");
                return false;
            }
        }

        #endregion
    }
}
