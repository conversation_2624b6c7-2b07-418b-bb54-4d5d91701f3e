# Task 05: Form Discovery Service Implementation

## Objective
Create an automatic form discovery service that scans the MainForms folder, detects new/removed forms, and synchronizes them with the permission system database. This ensures the permission system stays current with available forms.

## Priority
**CORE SERVICES** - Depends on Tasks 01-04

## Estimated Time
1 hour

## Dependencies
- Task 01: Database Schema Verification and Setup
- Task 02: Permission Data Models Creation
- Task 03: Forms Configuration Setup
- Task 04: Database Connection Service for Permissions

## Files to Create
- `Modules/Services/FormDiscoveryService.cs`
- `Modules/Services/FormSyncResult.cs`

## Form Discovery Service Implementation

### FormSyncResult.cs
```csharp
using System;
using System.Collections.Generic;

namespace ProManage.Modules.Services
{
    public class FormSyncResult
    {
        public bool Success { get; set; }
        public List<string> FormsAdded { get; set; } = new List<string>();
        public List<string> FormsRemoved { get; set; } = new List<string>();
        public List<string> Errors { get; set; } = new List<string>();
        public DateTime SyncTime { get; set; } = DateTime.Now;
        
        public bool HasChanges => FormsAdded.Count > 0 || FormsRemoved.Count > 0;
        
        public string GetSummary()
        {
            if (!Success)
                return $"Sync failed: {string.Join(", ", Errors)}";
                
            if (!HasChanges)
                return "No changes detected";
                
            var summary = new List<string>();
            if (FormsAdded.Count > 0)
                summary.Add($"Added {FormsAdded.Count} forms");
            if (FormsRemoved.Count > 0)
                summary.Add($"Removed {FormsRemoved.Count} forms");
                
            return string.Join(", ", summary);
        }
    }
}
```

### FormDiscoveryService.cs
```csharp
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using ProManage.Modules.Connections;
using ProManage.Modules.Models;

namespace ProManage.Modules.Services
{
    public static class FormDiscoveryService
    {
        private static readonly string MainFormsPath = Path.Combine(Application.StartupPath, "Forms", "MainForms");
        private static readonly PermissionDatabaseService _dbService = new PermissionDatabaseService();
        
        /// <summary>
        /// Scan MainForms folder and sync with database
        /// </summary>
        public static FormSyncResult SyncFormsWithDatabase()
        {
            var result = new FormSyncResult();
            
            try
            {
                // 1. Get forms from file system
                var fileSystemForms = GetFormsFromFileSystem();
                if (fileSystemForms == null)
                {
                    result.Errors.Add("Could not access MainForms directory");
                    return result;
                }
                
                // 2. Get forms from database
                var databaseForms = GetFormsFromDatabase();
                
                // 3. Find new forms (in filesystem but not in database)
                var newForms = fileSystemForms.Except(databaseForms, StringComparer.OrdinalIgnoreCase).ToList();
                
                // 4. Find removed forms (in database but not in filesystem)
                var removedForms = databaseForms.Except(fileSystemForms, StringComparer.OrdinalIgnoreCase).ToList();
                
                // 5. Add new forms to permission system
                foreach (var formName in newForms)
                {
                    if (AddFormToPermissionSystem(formName))
                    {
                        result.FormsAdded.Add(formName);
                    }
                    else
                    {
                        result.Errors.Add($"Failed to add form: {formName}");
                    }
                }
                
                // 6. Remove deleted forms from permission system
                foreach (var formName in removedForms)
                {
                    if (RemoveFormFromPermissionSystem(formName))
                    {
                        result.FormsRemoved.Add(formName);
                    }
                    else
                    {
                        result.Errors.Add($"Failed to remove form: {formName}");
                    }
                }
                
                // 7. Update forms configuration
                UpdateFormsConfiguration(result.FormsAdded, result.FormsRemoved);
                
                result.Success = result.Errors.Count == 0;
                return result;
            }
            catch (Exception ex)
            {
                result.Errors.Add($"Sync operation failed: {ex.Message}");
                return result;
            }
        }
        
        /// <summary>
        /// Get list of form files from MainForms directory
        /// </summary>
        public static List<string> GetFormsFromFileSystem()
        {
            try
            {
                if (!Directory.Exists(MainFormsPath))
                {
                    return null;
                }
                
                return Directory.GetFiles(MainFormsPath, "*.cs")
                    .Where(f => !f.EndsWith(".Designer.cs", StringComparison.OrdinalIgnoreCase))
                    .Where(f => !f.EndsWith(".resx", StringComparison.OrdinalIgnoreCase))
                    .Select(f => Path.GetFileNameWithoutExtension(f))
                    .Where(f => !string.IsNullOrEmpty(f))
                    .OrderBy(f => f)
                    .ToList();
            }
            catch
            {
                return null;
            }
        }
        
        /// <summary>
        /// Get list of forms from database permission system
        /// </summary>
        public static List<string> GetFormsFromDatabase()
        {
            try
            {
                // Get distinct form names from role_permissions table
                var rolePermissions = _dbService.GetRolePermissions(1); // Get from any role to get form list
                var allRoles = _dbService.GetAllRoles();
                
                var formNames = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
                
                foreach (var role in allRoles)
                {
                    var permissions = _dbService.GetRolePermissions(role.RoleId);
                    foreach (var permission in permissions)
                    {
                        formNames.Add(permission.FormName);
                    }
                }
                
                return formNames.OrderBy(f => f).ToList();
            }
            catch
            {
                return new List<string>();
            }
        }
        
        /// <summary>
        /// Add new form to permission system for all roles
        /// </summary>
        private static bool AddFormToPermissionSystem(string formName)
        {
            try
            {
                // Add to database with default permissions (false for all)
                var success = _dbService.AddFormToPermissionSystem(formName);
                
                if (success)
                {
                    // Log the addition
                    System.Diagnostics.Debug.WriteLine($"Added form to permission system: {formName}");
                }
                
                return success;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error adding form {formName}: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// Remove form from permission system
        /// </summary>
        private static bool RemoveFormFromPermissionSystem(string formName)
        {
            try
            {
                // Remove from database (both role_permissions and user_permissions)
                var success = _dbService.RemoveFormFromPermissionSystem(formName);
                
                if (success)
                {
                    // Log the removal
                    System.Diagnostics.Debug.WriteLine($"Removed form from permission system: {formName}");
                }
                
                return success;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error removing form {formName}: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// Update forms configuration file with changes
        /// </summary>
        private static void UpdateFormsConfiguration(List<string> addedForms, List<string> removedForms)
        {
            try
            {
                // Add new forms to configuration
                foreach (var formName in addedForms)
                {
                    var config = new FormConfiguration
                    {
                        FormName = formName,
                        DisplayName = GetDisplayNameFromFormName(formName),
                        Category = GetCategoryFromFormName(formName),
                        IsActive = true,
                        SortOrder = GetNextSortOrder()
                    };
                    
                    FormsConfigurationService.AddFormConfiguration(config);
                }
                
                // Remove deleted forms from configuration
                foreach (var formName in removedForms)
                {
                    FormsConfigurationService.RemoveFormConfiguration(formName);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating forms configuration: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Generate display name from form name
        /// </summary>
        private static string GetDisplayNameFromFormName(string formName)
        {
            // Convert PascalCase to readable format
            // Example: "UserMasterForm" -> "User Master"
            var result = formName.Replace("Form", "");
            
            // Add spaces before capital letters
            for (int i = result.Length - 1; i > 0; i--)
            {
                if (char.IsUpper(result[i]) && !char.IsUpper(result[i - 1]))
                {
                    result = result.Insert(i, " ");
                }
            }
            
            return result;
        }
        
        /// <summary>
        /// Determine category from form name
        /// </summary>
        private static string GetCategoryFromFormName(string formName)
        {
            var lowerName = formName.ToLower();
            
            if (lowerName.Contains("user") || lowerName.Contains("role") || lowerName.Contains("permission"))
                return "Security";
            
            if (lowerName.Contains("database") || lowerName.Contains("sql") || lowerName.Contains("parameter"))
                return "System";
            
            return "Business";
        }
        
        /// <summary>
        /// Get next sort order for new forms
        /// </summary>
        private static int GetNextSortOrder()
        {
            var allForms = FormsConfigurationService.GetAllForms();
            return allForms.Count > 0 ? allForms.Max(f => f.SortOrder) + 10 : 10;
        }
        
        /// <summary>
        /// Validate form file exists and is valid
        /// </summary>
        public static bool ValidateFormFile(string formName)
        {
            try
            {
                var formPath = Path.Combine(MainFormsPath, $"{formName}.cs");
                var designerPath = Path.Combine(MainFormsPath, $"{formName}.Designer.cs");
                
                return File.Exists(formPath) && File.Exists(designerPath);
            }
            catch
            {
                return false;
            }
        }
        
        /// <summary>
        /// Get sync status information
        /// </summary>
        public static string GetSyncStatus()
        {
            try
            {
                var fileSystemForms = GetFormsFromFileSystem();
                var databaseForms = GetFormsFromDatabase();
                
                if (fileSystemForms == null)
                    return "Error: Cannot access MainForms directory";
                
                var inSync = fileSystemForms.Count == databaseForms.Count &&
                           fileSystemForms.All(f => databaseForms.Contains(f, StringComparer.OrdinalIgnoreCase));
                
                return inSync 
                    ? $"In sync: {fileSystemForms.Count} forms"
                    : $"Out of sync: {fileSystemForms.Count} files, {databaseForms.Count} database entries";
            }
            catch (Exception ex)
            {
                return $"Error checking sync status: {ex.Message}";
            }
        }
    }
}
```

## Integration Points

### Application Startup
```csharp
// Add to MainFrame.cs or Program.cs
private void InitializePermissionSystem()
{
    try
    {
        var syncResult = FormDiscoveryService.SyncFormsWithDatabase();
        
        if (syncResult.HasChanges)
        {
            // Log or notify about changes
            System.Diagnostics.Debug.WriteLine($"Form sync: {syncResult.GetSummary()}");
        }
    }
    catch (Exception ex)
    {
        // Log error but don't prevent application startup
        System.Diagnostics.Debug.WriteLine($"Form sync error: {ex.Message}");
    }
}
```

### Manual Sync Option
Add menu option in DatabaseForm or admin tools to manually trigger sync.

## Acceptance Criteria

- [ ] Automatically detects new forms in MainForms folder
- [ ] Removes permissions for deleted forms
- [ ] Updates forms configuration automatically
- [ ] Provides detailed sync results and error reporting
- [ ] Safe to run multiple times without side effects
- [ ] Integrates with existing database service
- [ ] Generates appropriate display names and categories
- [ ] Validates form files before processing
- [ ] Provides sync status information

## Dependencies
- Task 01: Database Schema Verification and Setup
- Task 02: Permission Data Models Creation
- Task 03: Forms Configuration Setup
- Task 04: Database Connection Service for Permissions

## Next Tasks
This task enables:
- Task 06: Core Permission Service Logic
- Task 09: Permission Management Form (3-Tab UI)
- Task 13: MainFrame Ribbon Permission Filtering
