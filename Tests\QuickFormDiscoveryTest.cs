using System;
using System.Collections.Generic;
using System.Diagnostics;
using ProManage.Modules.Services;

namespace ProManage.Tests
{
    /// <summary>
    /// Quick test to verify Form Discovery Service critical functionality
    /// </summary>
    public static class QuickFormDiscoveryTest
    {
        public static void RunQuickTest()
        {
            Debug.WriteLine("=== Quick Form Discovery Test ===");
            
            // Test 1: Form Name Normalization
            Debug.WriteLine("Test 1: Form Name Normalization");
            var testName = "testForm";
            var normalized = FormDiscoveryService.NormalizeFormName(testName);
            Debug.WriteLine($"  Input: {testName} -> Output: {normalized}");
            Debug.WriteLine($"  Expected: TESTFORM, Actual: {normalized}, Pass: {normalized == "TESTFORM"}");
            
            // Test 2: Test File Filtering
            Debug.WriteLine("\nTest 2: Test File Filtering");
            var formNames = new List<string> 
            { 
                "MainForm", 
                "TestForm", 
                "SampleTests", 
                "UnitTests",
                "DatabaseForm",
                "PermissionTests",
                "FormDiscoveryServiceTests"
            };
            
            var filtered = FormDiscoveryService.FilterValidFormNames(formNames);
            Debug.WriteLine($"  Input: {string.Join(", ", formNames)}");
            Debug.WriteLine($"  Output: {string.Join(", ", filtered)}");
            
            bool hasTestFiles = false;
            foreach (var form in filtered)
            {
                if (form.EndsWith("Tests", StringComparison.OrdinalIgnoreCase) || 
                    form.EndsWith("Test", StringComparison.OrdinalIgnoreCase))
                {
                    hasTestFiles = true;
                    break;
                }
            }
            Debug.WriteLine($"  Test files excluded: {!hasTestFiles}");
            
            // Test 3: File Validation
            Debug.WriteLine("\nTest 3: File Validation");
            var testFiles = new Dictionary<string, bool>
            {
                { "MainForm.cs", true },
                { "MainForm.Designer.cs", false },
                { "TestForm.resx", false },
                { "DatabaseForm.cs", true }
            };
            
            foreach (var testFile in testFiles)
            {
                var isValid = FormDiscoveryService.IsValidFormFile(testFile.Key);
                Debug.WriteLine($"  {testFile.Key} -> {isValid} (Expected: {testFile.Value}, Pass: {isValid == testFile.Value})");
            }
            
            // Test 4: Cache Hash Generation
            Debug.WriteLine("\nTest 4: Cache Hash Generation");
            var forms1 = new List<string> { "FormA", "FormB", "FormC" };
            var forms2 = new List<string> { "FormC", "FormA", "FormB" }; // Different order
            
            var hash1 = FormScanCacheService.GenerateFormListHash(forms1);
            var hash2 = FormScanCacheService.GenerateFormListHash(forms2);
            
            Debug.WriteLine($"  Hash 1: {hash1}");
            Debug.WriteLine($"  Hash 2: {hash2}");
            Debug.WriteLine($"  Hashes equal (order independent): {hash1 == hash2}");
            
            Debug.WriteLine("\n=== Quick Test Complete ===");
        }
    }
}
