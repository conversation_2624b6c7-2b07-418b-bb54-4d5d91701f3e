# 🔧 Core Services Implementation

## 📋 OVERVIEW

This task covers the implementation of all core service classes required for the Form Discovery Permission Sync system. These services form the backbone of the entire system.

## 🎯 OBJECTIVES

- Enhance existing FormDiscoveryService with caching and filtering
- Create PermissionSyncService with transaction safety
- Implement FormScanCacheService for persistent caching
- Build GlobalSyncMutexService for thread safety
- Develop SyncLoggingService with Serilog integration

## 🏗️ SERVICE IMPLEMENTATIONS

### 1. FormDiscoveryService.cs (Enhanced) - **CRITICAL**

**Location**: `Modules/Services/FormDiscoveryService.cs`
**Type**: Enhancement of existing service

#### Key Methods to Add/Enhance
```csharp
public static List<string> GetFormsFromFileSystem()
public static List<string> GetFormsFromDatabase()
public static FormSyncResult CompareFormsWithDatabase()
public static bool IsValidFormFile(string filePath)
public static List<string> FilterValidFormNames(List<string> formNames) // **CRITICAL**: Remove *Tests.cs
public static string NormalizeFormName(string formName) // **CRITICAL**: UpperInvariant standard
```

#### **CRITICAL** Implementation Requirements
- **Test File Filtering**: Exclude *Tests.cs files to avoid test harness permissions
- **Form Name Normalization**: Use UpperInvariant standard for consistency
- **Case-Insensitive Comparison**: Prevent duplicate form entries
- **File Validation**: Only process .cs files, exclude .designer.cs and .resx

#### Implementation Logic
1. Scan MainForms folder for .cs files (exclude .designer.cs, .resx)
2. Extract form names by removing .cs extension
3. Filter out test files (*Tests.cs pattern)
4. Normalize form names using UpperInvariant
5. Compare with database form names (case-insensitive)
6. Return mismatch results with missing/obsolete form lists

### 2. PermissionSyncService.cs (New) - **CRITICAL**

**Location**: `Modules/Services/PermissionSyncService.cs`
**Type**: New service component

#### Key Methods
```csharp
public static bool AddMissingFormsToPermissions(List<string> formNames, IProgress<SyncProgress> progress = null)
public static bool RemoveObsoleteFormsFromPermissions(List<string> formNames)
public static FormSyncResult ExecuteFullSync(IProgress<SyncProgress> progress = null, TimeSpan? timeout = null)
public static bool ValidatePermissionTables()
public static void ReportProgress(IProgress<SyncProgress> progress, int completed, int total, string operation)
public static TimeSpan GetConfigurableTimeout() // Read from appsettings.json
```

#### **CRITICAL** Implementation Requirements
- **5-Minute TransactionScope**: Default timeout, configurable via appsettings.json
- **SERIALIZABLE Isolation**: Wrap all operations in TransactionScope with SERIALIZABLE isolation
- **Progress Reporting**: Use IProgress<SyncProgress> with UI thread marshaling
- **Batch Operations**: Handle large datasets efficiently
- **Error Handling**: Complete rollback on any failure

#### Implementation Logic
1. Add missing forms to all users and roles with default permissions (false)
2. Remove obsolete forms from both permission tables
3. Wrap all database operations in TransactionScope with configurable timeout
4. Use proper transaction rollback strategy on failures
5. Report progress via IProgress<SyncProgress> with UI thread marshaling
6. Handle batch operations efficiently with detailed progress updates

### 3. FormScanCacheService.cs (New) - **CRITICAL**

**Location**: `Modules/Services/FormScanCacheService.cs`
**Type**: New caching service

#### Key Methods
```csharp
public static FormScanCache GetCache()
public static void UpdateCache(List<string> formList)
public static bool ShouldSkipScan()
public static string GenerateFormListHash(List<string> forms)
public static void SaveCacheToDisk(FormScanCache cache)
public static FormScanCache LoadCacheFromDisk()
public static string GetCacheFilePath() // %APPDATA%/ProManage/cache.json
public static bool ValidateCacheVersion(FormScanCache cache)
public static FormScanCache MigrateCacheVersion(FormScanCache oldCache)
```

#### **CRITICAL** Implementation Requirements
- **Persistent Storage**: Cache to `%APPDATA%/ProManage/cache.json`
- **Fallback Path**: Use `%LOCALAPPDATA%/ProManage/cache.json` if %APPDATA% fails
- **Cache Versioning**: Support schema migration with version tracking
- **30-Minute Expiration**: Balance performance with accuracy
- **Corruption Recovery**: Graceful fallback on corrupted cache files

#### Implementation Logic
1. Persistent cache storage survives application restarts
2. Cache last scan timestamp and form list hash
3. Skip detection if nothing changed since last run
4. Invalidate cache on Refresh button click
5. 30-minute cache expiration for safety
6. Graceful fallback if cache file is corrupted or version mismatch

### 4. GlobalSyncMutexService.cs (New) - **CRITICAL**

**Location**: `Modules/Services/GlobalSyncMutexService.cs`
**Type**: New singleton service

#### Key Methods
```csharp
public static bool TryAcquireSyncLock(TimeSpan timeout)
public static void ReleaseSyncLock()
public static bool IsSyncInProgress()
public static bool TryAcquireDbAdvisoryLock(string connectionString) // Cross-machine safety
public static void ReleaseDbAdvisoryLock(string connectionString)
public static long GenerateAdvisoryLockKey() // Hash-based key generation
```

#### **CRITICAL** Implementation Requirements
- **Process-Level Mutex**: Static Mutex to prevent app-wide concurrent syncs
- **Cross-Machine Safety**: PostgreSQL advisory lock with hash-based key generation
- **Advisory Lock Key**: Generated from `hashtext('ProManage_FormSync')` to prevent collisions
- **Timeout Handling**: Graceful fallback when locks are held

#### Advisory Lock Key Generation
```csharp
public static long GenerateAdvisoryLockKey()
{
    // Use consistent hash to avoid collisions with other tools
    return BitConverter.ToInt64(SHA256.HashData(Encoding.UTF8.GetBytes("ProManage_FormSync")), 0);
}
```

### 5. SyncLoggingService.cs (New)

**Location**: `Modules/Services/SyncLoggingService.cs`
**Type**: New logging service

#### Key Methods
```csharp
public static void LogSyncStart(int totalForms)
public static void LogSyncComplete(int addedCount, int removedCount, TimeSpan elapsed)
public static void LogSyncError(string operation, Exception ex)
public static void LogPerformanceMetrics(SyncPerformanceMetrics metrics)
public static void ConfigureRollingFileLogger() // 30-day retention, 100MB max
```

#### Serilog Configuration
```csharp
// Rolling file with retention policy and log level management
Log.Logger = new LoggerConfiguration()
    .MinimumLevel.Information() // Info level for milestones
    .MinimumLevel.Override("ProManage.Sync.Progress", LogEventLevel.Debug) // Debug for progress loops
    .WriteTo.File("logs/sync-.log",
        rollingInterval: RollingInterval.Day,
        retainedFileCountLimit: 30,
        fileSizeLimitBytes: 100_000_000)
    .CreateLogger();
```

## 🔗 DEPENDENCIES

### Prerequisites
- FormSyncModels.cs (see Task 02)
- Database procedures (see Task 03)
- Serilog NuGet package installation

### Service Dependencies
- FormDiscoveryService → FormScanCacheService
- PermissionSyncService → GlobalSyncMutexService
- All services → SyncLoggingService

## ✅ SUCCESS CRITERIA

### Core Functionality
- [x] FormDiscoveryService enhanced with caching and filtering
- [x] PermissionSyncService created with transaction safety
- [x] FormScanCacheService implemented with persistent storage
- [x] GlobalSyncMutexService prevents concurrent operations
- [x] SyncLoggingService provides comprehensive logging

### **CRITICAL** Requirements Met
- [x] Test file filtering (*Tests.cs excluded)
- [x] Form name normalization (UpperInvariant)
- [x] 5-minute TransactionScope with configurable timeout
- [x] Persistent cache survives app restarts
- [x] Cross-machine advisory locks prevent conflicts
- [x] Progress reporting with UI thread marshaling

### Quality Assurance
- [x] All services handle errors gracefully
- [x] Comprehensive logging at appropriate levels
- [x] Thread-safe operations
- [x] Performance optimized with caching
- [x] Memory efficient implementations

### Implementation Notes
- **COMPLETED**: All core services implemented successfully
- **NOTE**: Serilog package needs to be installed for full logging functionality (currently using Debug.WriteLine fallback)
- **COMPLETED**: Added missing methods to PermissionDatabaseService: AddRolePermission, AddUserPermission, RemoveFormFromRolePermissions, RemoveFormFromUserPermissions
- **COMPLETED**: All services follow ProManage naming conventions and error handling patterns

## 🧪 TESTING REQUIREMENTS

### Unit Tests Required
- FormDiscoveryService: File scanning, filtering, normalization
- PermissionSyncService: Transaction handling, progress reporting
- FormScanCacheService: Cache operations, versioning, migration
- GlobalSyncMutexService: Lock acquisition, timeout handling
- SyncLoggingService: Log configuration, message formatting

### Integration Tests Required
- Service interaction workflows
- Database transaction rollback scenarios
- Cache persistence across application restarts
- Cross-machine lock coordination

## 📝 IMPLEMENTATION NOTES

### Error Handling Strategy
- Use try-catch blocks with specific exception types
- Log all errors with context information
- Provide user-friendly error messages
- Ensure proper resource cleanup

### Performance Considerations
- Minimize file system operations with caching
- Use efficient database queries
- Implement progress reporting for long operations
- Optimize memory usage for large form lists

### Security Considerations
- Validate all file paths to prevent directory traversal
- Use parameterized database queries
- Implement proper access controls for cache files
- Secure advisory lock key generation

---

**Next Task**: [02-Data-Models-And-Interfaces.md](./02-Data-Models-And-Interfaces.md)
