using System;
using System.Collections.Generic;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using ProManage.Modules.Models;

namespace ProManage.Modules.Services
{
    /// <summary>
    /// Service for managing persistent form scan cache
    /// Provides 30-minute cache expiration with corruption recovery
    /// </summary>
    public static class FormScanCacheService
    {
        private static readonly string CacheFileName = "cache.json";
        private static FormScanCache _memoryCache;

        /// <summary>
        /// Get cached form scan results
        /// </summary>
        /// <returns>Cache object or null if invalid/expired</returns>
        public static FormScanCache GetCache()
        {
            try
            {
                // Check memory cache first
                if (_memoryCache != null && _memoryCache.IsValid)
                {
                    return _memoryCache;
                }

                // Load from disk
                var cache = LoadCacheFromDisk();
                if (cache != null && ValidateCacheVersion(cache) && cache.IsValid)
                {
                    _memoryCache = cache;
                    return cache;
                }

                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting cache: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Update cache with new form list
        /// </summary>
        /// <param name="formList">List of forms to cache</param>
        public static void UpdateCache(List<string> formList)
        {
            try
            {
                var cache = new FormScanCache
                {
                    Version = 1,
                    LastScanTime = DateTime.Now,
                    FormListHash = GenerateFormListHash(formList),
                    CachedFormList = new List<string>(formList),
                    HashingAlgorithm = "SHA256",
                    CacheFilePath = GetCacheFilePath()
                };

                SaveCacheToDisk(cache);
                _memoryCache = cache;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating cache: {ex.Message}");
            }
        }

        /// <summary>
        /// Check if scan should be skipped based on cache validity
        /// </summary>
        /// <returns>True if scan can be skipped</returns>
        public static bool ShouldSkipScan()
        {
            var cache = GetCache();
            return cache != null && cache.IsValid;
        }

        /// <summary>
        /// Generate SHA256 hash of form list for change detection
        /// </summary>
        /// <param name="forms">List of form names</param>
        /// <returns>SHA256 hash string</returns>
        public static string GenerateFormListHash(List<string> forms)
        {
            try
            {
                if (forms == null || forms.Count == 0)
                    return string.Empty;

                // Sort forms for consistent hashing
                var sortedForms = new List<string>(forms);
                sortedForms.Sort(StringComparer.OrdinalIgnoreCase);

                var combined = string.Join("|", sortedForms);
                using (var sha256 = SHA256.Create())
                {
                    var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(combined));
                    return Convert.ToBase64String(hashBytes);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error generating hash: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// Save cache to disk with error handling
        /// </summary>
        /// <param name="cache">Cache object to save</param>
        public static void SaveCacheToDisk(FormScanCache cache)
        {
            try
            {
                var cacheFilePath = GetCacheFilePath();
                var directory = Path.GetDirectoryName(cacheFilePath);

                // Ensure directory exists
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                cache.CacheFilePath = cacheFilePath;
                var json = JsonSerializer.Serialize(cache, new JsonSerializerOptions 
                { 
                    WriteIndented = true 
                });

                File.WriteAllText(cacheFilePath, json);
                cache.CacheFileSize = new FileInfo(cacheFilePath).Length;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving cache to disk: {ex.Message}");
                
                // Try fallback location
                try
                {
                    var fallbackPath = GetFallbackCacheFilePath();
                    var fallbackDir = Path.GetDirectoryName(fallbackPath);
                    
                    if (!Directory.Exists(fallbackDir))
                    {
                        Directory.CreateDirectory(fallbackDir);
                    }

                    cache.CacheFilePath = fallbackPath;
                    var json = JsonSerializer.Serialize(cache, new JsonSerializerOptions 
                    { 
                        WriteIndented = true 
                    });

                    File.WriteAllText(fallbackPath, json);
                    cache.CacheFileSize = new FileInfo(fallbackPath).Length;
                }
                catch (Exception fallbackEx)
                {
                    System.Diagnostics.Debug.WriteLine($"Error saving to fallback cache: {fallbackEx.Message}");
                }
            }
        }

        /// <summary>
        /// Load cache from disk with corruption recovery
        /// </summary>
        /// <returns>Cache object or null if not found/corrupted</returns>
        public static FormScanCache LoadCacheFromDisk()
        {
            try
            {
                var cacheFilePath = GetCacheFilePath();
                if (File.Exists(cacheFilePath))
                {
                    var json = File.ReadAllText(cacheFilePath);
                    var cache = JsonSerializer.Deserialize<FormScanCache>(json);
                    
                    if (ValidateCacheVersion(cache))
                    {
                        return cache;
                    }
                }

                // Try fallback location
                var fallbackPath = GetFallbackCacheFilePath();
                if (File.Exists(fallbackPath))
                {
                    var json = File.ReadAllText(fallbackPath);
                    var cache = JsonSerializer.Deserialize<FormScanCache>(json);
                    
                    if (ValidateCacheVersion(cache))
                    {
                        return cache;
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading cache from disk: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Get primary cache file path
        /// </summary>
        /// <returns>Full path to cache file</returns>
        public static string GetCacheFilePath()
        {
            var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
            return Path.Combine(appDataPath, "ProManage", CacheFileName);
        }

        /// <summary>
        /// Get fallback cache file path
        /// </summary>
        /// <returns>Full path to fallback cache file</returns>
        public static string GetFallbackCacheFilePath()
        {
            var localAppDataPath = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);
            return Path.Combine(localAppDataPath, "ProManage", CacheFileName);
        }

        /// <summary>
        /// Validate cache version and migrate if needed
        /// </summary>
        /// <param name="cache">Cache to validate</param>
        /// <returns>True if cache is valid</returns>
        public static bool ValidateCacheVersion(FormScanCache cache)
        {
            if (cache == null) return false;

            // Current version is 1, so accept version 1
            if (cache.Version == 1)
            {
                return true;
            }

            // Future: Handle version migration here
            return false;
        }

        /// <summary>
        /// Migrate cache from older version (future implementation)
        /// </summary>
        /// <param name="oldCache">Old cache to migrate</param>
        /// <returns>Migrated cache or null if migration fails</returns>
        public static FormScanCache MigrateCacheVersion(FormScanCache oldCache)
        {
            // Future implementation for cache version migration
            return null;
        }

        /// <summary>
        /// Clear all cached data
        /// </summary>
        public static void ClearCache()
        {
            try
            {
                _memoryCache = null;

                var cacheFilePath = GetCacheFilePath();
                if (File.Exists(cacheFilePath))
                {
                    File.Delete(cacheFilePath);
                }

                var fallbackPath = GetFallbackCacheFilePath();
                if (File.Exists(fallbackPath))
                {
                    File.Delete(fallbackPath);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error clearing cache: {ex.Message}");
            }
        }
    }
}
