# Task 02: Permission Data Models Creation

## Objective
Create comprehensive data models for the RBAC permission system. These models will represent database entities and provide type-safe access to permission data throughout the application.

## Priority
**FOUNDATION** - Depends on Task 01

## Estimated Time
1 hour

## Dependencies
- Task 01: Database Schema Verification and Setup (must be completed)

## Files to Create
- `Modules/Models/PermissionModels.cs`
- `Modules/Models/RoleModels.cs`
- `Modules/Models/UserPermissionModels.cs`

## Data Models Required

### 1. Role Model
```csharp
public class Role
{
    public int RoleId { get; set; }
    public string RoleName { get; set; }
    public string Description { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedDate { get; set; }
    public DateTime ModifiedDate { get; set; }
}
```

### 2. Permission Models
```csharp
public class RolePermission
{
    public int PermissionId { get; set; }
    public int RoleId { get; set; }
    public string FormName { get; set; }
    public bool ReadPermission { get; set; }
    public bool NewPermission { get; set; }
    public bool EditPermission { get; set; }
    public bool DeletePermission { get; set; }
    public bool PrintPermission { get; set; }
    public DateTime CreatedDate { get; set; }
}

public class UserPermission
{
    public int UserPermissionId { get; set; }
    public int UserId { get; set; }
    public string FormName { get; set; }
    public bool? ReadPermission { get; set; }    // NULL = inherit from role
    public bool? NewPermission { get; set; }     // NULL = inherit from role
    public bool? EditPermission { get; set; }    // NULL = inherit from role
    public bool? DeletePermission { get; set; }  // NULL = inherit from role
    public bool? PrintPermission { get; set; }   // NULL = inherit from role
    public DateTime CreatedDate { get; set; }
}

public class GlobalPermission
{
    public int GlobalPermissionId { get; set; }
    public int UserId { get; set; }
    public bool CanCreateUsers { get; set; }
    public bool CanEditUsers { get; set; }
    public bool CanDeleteUsers { get; set; }
    public bool CanPrintUsers { get; set; }
    public DateTime CreatedDate { get; set; }
}
```

### 3. Composite Models for UI
```csharp
public class EffectivePermission
{
    public string FormName { get; set; }
    public bool ReadPermission { get; set; }
    public bool NewPermission { get; set; }
    public bool EditPermission { get; set; }
    public bool DeletePermission { get; set; }
    public bool PrintPermission { get; set; }
    public PermissionSource Source { get; set; }  // Role or UserOverride
}

public enum PermissionSource
{
    Role,
    UserOverride
}

public class UserWithPermissions
{
    public int UserId { get; set; }
    public string Username { get; set; }
    public string FullName { get; set; }
    public int RoleId { get; set; }
    public string RoleName { get; set; }
    public List<EffectivePermission> FormPermissions { get; set; }
    public GlobalPermission GlobalPermissions { get; set; }
}
```

### 4. Permission Request Models
```csharp
public class PermissionRequest
{
    public int UserId { get; set; }
    public string FormName { get; set; }
    public PermissionType PermissionType { get; set; }
}

public enum PermissionType
{
    Read,
    New,
    Edit,
    Delete,
    Print
}

public class GlobalPermissionRequest
{
    public int UserId { get; set; }
    public GlobalPermissionType PermissionType { get; set; }
}

public enum GlobalPermissionType
{
    CanCreateUsers,
    CanEditUsers,
    CanDeleteUsers,
    CanPrintUsers
}
```

### 5. Form Configuration Model
```csharp
public class FormConfiguration
{
    public string FormName { get; set; }
    public string DisplayName { get; set; }
    public string Category { get; set; }
    public bool IsActive { get; set; }
    public int SortOrder { get; set; }
}

public class FormsConfigurationCollection
{
    public List<FormConfiguration> Forms { get; set; }
    public DateTime LastUpdated { get; set; }
}
```

### 6. Permission Update Models
```csharp
public class RolePermissionUpdate
{
    public int RoleId { get; set; }
    public string FormName { get; set; }
    public bool ReadPermission { get; set; }
    public bool NewPermission { get; set; }
    public bool EditPermission { get; set; }
    public bool DeletePermission { get; set; }
    public bool PrintPermission { get; set; }
}

public class UserPermissionUpdate
{
    public int UserId { get; set; }
    public string FormName { get; set; }
    public bool? ReadPermission { get; set; }
    public bool? NewPermission { get; set; }
    public bool? EditPermission { get; set; }
    public bool? DeletePermission { get; set; }
    public bool? PrintPermission { get; set; }
}

public class GlobalPermissionUpdate
{
    public int UserId { get; set; }
    public bool CanCreateUsers { get; set; }
    public bool CanEditUsers { get; set; }
    public bool CanDeleteUsers { get; set; }
    public bool CanPrintUsers { get; set; }
}
```

## Implementation Guidelines

### File Organization
- **PermissionModels.cs**: Core permission classes and enums
- **RoleModels.cs**: Role-related models and collections
- **UserPermissionModels.cs**: User-specific permission models

### Naming Conventions
- Use descriptive property names matching database columns
- Use PascalCase for all public properties
- Use nullable bool (bool?) for user permissions to represent inheritance
- Include Created/Modified dates for audit trail

### Validation Attributes
```csharp
using System.ComponentModel.DataAnnotations;

public class Role
{
    [Required]
    [StringLength(50)]
    public string RoleName { get; set; }
    
    [StringLength(255)]
    public string Description { get; set; }
}
```

## Acceptance Criteria

- [ ] All data models created with proper property types
- [ ] Nullable bool properties for user permissions (inheritance support)
- [ ] Enum types for permission and form categories
- [ ] Composite models for UI data binding
- [ ] Request/Update models for service operations
- [ ] Proper validation attributes where needed
- [ ] XML documentation for all public classes and properties
- [ ] Models follow ProManage naming conventions

## Dependencies
- Task 01: Database Schema Verification and Setup

## Next Tasks
This task enables:
- Task 03: Forms Configuration Setup
- Task 04: Database Connection Service for Permissions
- Task 05: Form Discovery Service Implementation
