# Task 03: Forms Configuration Setup

## Objective
Create a centralized configuration system for managing form definitions, display names, categories, and metadata. This configuration will be used by the permission system and form discovery service.

## Priority
**FOUNDATION** - Depends on Tasks 01-02

## Estimated Time
45 minutes

## Dependencies
- Task 01: Database Schema Verification and Setup
- Task 02: Permission Data Models Creation

## Files to Create
- `Modules/Config/FormsConfig.json`
- `Modules/Services/FormsConfigurationService.cs`

## Forms Configuration Structure

### FormsConfig.json
```json
{
  "lastUpdated": "2024-01-01T00:00:00Z",
  "forms": [
    {
      "formName": "DatabaseForm",
      "displayName": "Database Management",
      "category": "System",
      "isActive": true,
      "sortOrder": 10,
      "description": "Database connection and management tools",
      "requiresSpecialPermission": false
    },
    {
      "formName": "ParametersForm",
      "displayName": "System Parameters",
      "category": "System",
      "isActive": true,
      "sortOrder": 20,
      "description": "System-wide parameter configuration",
      "requiresSpecialPermission": false
    },
    {
      "formName": "RoleMasterForm",
      "displayName": "Role Management",
      "category": "Security",
      "isActive": true,
      "sortOrder": 30,
      "description": "Manage user roles and permissions",
      "requiresSpecialPermission": true
    },
    {
      "formName": "SQLQueryForm",
      "displayName": "SQL Query Tool",
      "category": "System",
      "isActive": true,
      "sortOrder": 40,
      "description": "Execute SQL queries and scripts",
      "requiresSpecialPermission": true
    },
    {
      "formName": "UserManagementListForm",
      "displayName": "User List",
      "category": "Security",
      "isActive": true,
      "sortOrder": 50,
      "description": "View and manage user accounts",
      "requiresSpecialPermission": false
    },
    {
      "formName": "UserMasterForm",
      "displayName": "User Management",
      "category": "Security",
      "isActive": true,
      "sortOrder": 60,
      "description": "Create and edit user accounts",
      "requiresSpecialPermission": false
    }
  ],
  "categories": [
    {
      "categoryName": "System",
      "displayName": "System Administration",
      "sortOrder": 10,
      "isActive": true
    },
    {
      "categoryName": "Security",
      "displayName": "Security & Access",
      "sortOrder": 20,
      "isActive": true
    }
  ]
}
```

### FormsConfigurationService.cs
```csharp
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Newtonsoft.Json;
using ProManage.Modules.Models;

namespace ProManage.Modules.Services
{
    public class FormsConfigurationService
    {
        private static readonly string ConfigFilePath = Path.Combine(
            Application.StartupPath, "Modules", "Config", "FormsConfig.json");
        
        private static FormsConfigurationCollection _cachedConfig;
        private static DateTime _lastLoadTime = DateTime.MinValue;
        
        /// <summary>
        /// Get all form configurations
        /// </summary>
        public static List<FormConfiguration> GetAllForms()
        {
            LoadConfigurationIfNeeded();
            return _cachedConfig?.Forms?.Where(f => f.IsActive).ToList() ?? new List<FormConfiguration>();
        }
        
        /// <summary>
        /// Get form configuration by name
        /// </summary>
        public static FormConfiguration GetFormConfiguration(string formName)
        {
            LoadConfigurationIfNeeded();
            return _cachedConfig?.Forms?.FirstOrDefault(f => 
                f.FormName.Equals(formName, StringComparison.OrdinalIgnoreCase) && f.IsActive);
        }
        
        /// <summary>
        /// Get forms by category
        /// </summary>
        public static List<FormConfiguration> GetFormsByCategory(string category)
        {
            LoadConfigurationIfNeeded();
            return _cachedConfig?.Forms?.Where(f => 
                f.Category.Equals(category, StringComparison.OrdinalIgnoreCase) && f.IsActive)
                .OrderBy(f => f.SortOrder)
                .ToList() ?? new List<FormConfiguration>();
        }
        
        /// <summary>
        /// Check if form exists in configuration
        /// </summary>
        public static bool FormExists(string formName)
        {
            return GetFormConfiguration(formName) != null;
        }
        
        /// <summary>
        /// Get display name for form
        /// </summary>
        public static string GetFormDisplayName(string formName)
        {
            var config = GetFormConfiguration(formName);
            return config?.DisplayName ?? formName;
        }
        
        /// <summary>
        /// Add new form to configuration
        /// </summary>
        public static bool AddFormConfiguration(FormConfiguration formConfig)
        {
            try
            {
                LoadConfigurationIfNeeded();
                
                if (_cachedConfig.Forms.Any(f => f.FormName.Equals(formConfig.FormName, StringComparison.OrdinalIgnoreCase)))
                {
                    return false; // Form already exists
                }
                
                _cachedConfig.Forms.Add(formConfig);
                _cachedConfig.LastUpdated = DateTime.UtcNow;
                
                SaveConfiguration();
                return true;
            }
            catch
            {
                return false;
            }
        }
        
        /// <summary>
        /// Remove form from configuration
        /// </summary>
        public static bool RemoveFormConfiguration(string formName)
        {
            try
            {
                LoadConfigurationIfNeeded();
                
                var form = _cachedConfig.Forms.FirstOrDefault(f => 
                    f.FormName.Equals(formName, StringComparison.OrdinalIgnoreCase));
                
                if (form != null)
                {
                    _cachedConfig.Forms.Remove(form);
                    _cachedConfig.LastUpdated = DateTime.UtcNow;
                    SaveConfiguration();
                    return true;
                }
                
                return false;
            }
            catch
            {
                return false;
            }
        }
        
        /// <summary>
        /// Reload configuration from file
        /// </summary>
        public static void ReloadConfiguration()
        {
            _cachedConfig = null;
            _lastLoadTime = DateTime.MinValue;
            LoadConfigurationIfNeeded();
        }
        
        private static void LoadConfigurationIfNeeded()
        {
            if (_cachedConfig == null || 
                (File.Exists(ConfigFilePath) && File.GetLastWriteTime(ConfigFilePath) > _lastLoadTime))
            {
                LoadConfiguration();
            }
        }
        
        private static void LoadConfiguration()
        {
            try
            {
                if (File.Exists(ConfigFilePath))
                {
                    var json = File.ReadAllText(ConfigFilePath);
                    _cachedConfig = JsonConvert.DeserializeObject<FormsConfigurationCollection>(json);
                    _lastLoadTime = DateTime.Now;
                }
                else
                {
                    CreateDefaultConfiguration();
                }
            }
            catch
            {
                CreateDefaultConfiguration();
            }
        }
        
        private static void CreateDefaultConfiguration()
        {
            _cachedConfig = new FormsConfigurationCollection
            {
                LastUpdated = DateTime.UtcNow,
                Forms = GetDefaultFormConfigurations()
            };
            
            SaveConfiguration();
        }
        
        private static void SaveConfiguration()
        {
            try
            {
                var directory = Path.GetDirectoryName(ConfigFilePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }
                
                var json = JsonConvert.SerializeObject(_cachedConfig, Formatting.Indented);
                File.WriteAllText(ConfigFilePath, json);
                _lastLoadTime = DateTime.Now;
            }
            catch (Exception ex)
            {
                // Log error but don't throw - configuration service should be resilient
                System.Diagnostics.Debug.WriteLine($"Error saving forms configuration: {ex.Message}");
            }
        }
        
        private static List<FormConfiguration> GetDefaultFormConfigurations()
        {
            return new List<FormConfiguration>
            {
                new FormConfiguration { FormName = "DatabaseForm", DisplayName = "Database Management", Category = "System", IsActive = true, SortOrder = 10 },
                new FormConfiguration { FormName = "ParametersForm", DisplayName = "System Parameters", Category = "System", IsActive = true, SortOrder = 20 },
                new FormConfiguration { FormName = "RoleMasterForm", DisplayName = "Role Management", Category = "Security", IsActive = true, SortOrder = 30 },
                new FormConfiguration { FormName = "SQLQueryForm", DisplayName = "SQL Query Tool", Category = "System", IsActive = true, SortOrder = 40 },
                new FormConfiguration { FormName = "UserManagementListForm", DisplayName = "User List", Category = "Security", IsActive = true, SortOrder = 50 },
                new FormConfiguration { FormName = "UserMasterForm", DisplayName = "User Management", Category = "Security", IsActive = true, SortOrder = 60 }
            };
        }
    }
}
```

## Implementation Guidelines

### Configuration Management
- JSON-based configuration for easy editing
- Automatic file watching and reloading
- Fallback to default configuration if file is missing/corrupt
- Thread-safe caching mechanism

### Form Categories
- System: Database, Parameters, SQL tools
- Security: User and role management
- Business: Future business forms

### Error Handling
- Graceful degradation if configuration file is missing
- Default configurations for all current MainForms
- Logging of configuration errors without breaking functionality

## Acceptance Criteria

- [ ] FormsConfig.json created with all current MainForms
- [ ] FormsConfigurationService provides all required methods
- [ ] Configuration automatically reloads when file changes
- [ ] Default configuration created if file missing
- [ ] Thread-safe caching implementation
- [ ] Form existence validation methods
- [ ] Display name resolution for UI
- [ ] Category-based form grouping

## Dependencies
- Task 01: Database Schema Verification and Setup
- Task 02: Permission Data Models Creation

## Next Tasks
This task enables:
- Task 05: Form Discovery Service Implementation
- Task 09: Permission Management Form (3-Tab UI)
- Task 13: MainFrame Ribbon Permission Filtering
