# Permission System Hierarchy Implementation Tasks

## Overview
Implementation tasks for the corrected ProManage permission system hierarchy with MenuRibbon UC integration. The system calculates final permissions for each user and feeds them to MenuRibbon UC controls on every form.

## Phase 1: Database Service Updates ⏳

### Task 1.1: Update Permission Database Service
**File:** `Modules/Connections/PermissionDatabaseService.cs`
**Status:** ❌ TODO
**Priority:** HIGH
**Dependencies:** None

**Requirements:**
- Update queries to use users table fields (new_perm, edit_perm, delete_perm, print_perm)
- Remove references to global_permissions table
- Handle existing user_perm_overrides field properly
- Add methods for MenuRibbon UC permission queries

**Acceptance Criteria:**
- [ ] All global_permissions table references removed
- [ ] Users table fields properly queried
- [ ] user_perm_overrides field handled correctly
- [ ] Methods available for MenuRibbon UC integration

---

## Phase 2: UI Implementation ⏳

### Task 2.1: Identify and Update User Entry Form
**File:** TBD (Need to identify correct User Entry/Add User form)
**Status:** ❌ TODO
**Priority:** HIGH
**Dependencies:** Task 1.1

**Requirements:**
- Identify correct User Entry/Add User form
- Add/update permission tab with chkUserPermOverrides checkbox
- Link checkbox to existing user_perm_overrides field in users table
- Add proper data binding and validation
- Update save/load procedures to handle override flag

**Acceptance Criteria:**
- [ ] User Entry form identified
- [ ] chkUserPermOverrides checkbox implemented
- [ ] Data binding to user_perm_overrides field working
- [ ] Save/load procedures updated

### Task 2.2: Update Permission Management Form
**File:** `Forms/MainForms/PermissionManagementForm.cs`
**Status:** ❌ TODO
**Priority:** HIGH
**Dependencies:** Task 1.1

**Requirements:**
- Update chkPermissionOverride checkbox (read-only) in User Permissions tab
- User permissions grid editable only when user_perm_overrides = TRUE
- Display override status and visual indicators for inherited vs overridden permissions
- Remove global_permissions table references

**Acceptance Criteria:**
- [ ] chkPermissionOverride checkbox shows correct status
- [ ] Grid editability controlled by override flag
- [ ] Visual indicators for inherited vs overridden permissions
- [ ] global_permissions table references removed

---

## Phase 3: MenuRibbon UC Integration ⏳

### Task 3.1: Implement MenuRibbon UC Final Permission Logic
**File:** `Forms/ReusableForms/MenuRibbon.cs`
**Status:** ❌ TODO
**Priority:** CRITICAL
**Dependencies:** Task 1.1, Task 2.1, Task 2.2

**Requirements:**
- Implement HasEffectivePermission method for final permission calculation
- Check Global Permission from users table (new_perm, edit_perm, delete_perm, print_perm)
- Apply form-specific permission based on user_perm_overrides flag
- Integrate with existing ribbon button logic from RibbonButtonLogic.md
- Control New/Edit/Delete/Print buttons based on final permissions and form state

**Acceptance Criteria:**
- [ ] HasEffectivePermission method implemented
- [ ] Global permission checking from users table
- [ ] Override flag logic implemented
- [ ] Integration with RibbonButtonLogic.md patterns
- [ ] Button control logic working correctly

### Task 3.2: Update Permission Service Core Logic
**File:** `Modules/Services/PermissionService.cs`
**Status:** ❌ TODO
**Priority:** HIGH
**Dependencies:** Task 1.1

**Requirements:**
- Update permission resolution algorithm to support MenuRibbon UC
- Remove global_permissions table references
- Provide methods for MenuRibbon UC to query final permissions
- Implement permission hierarchy logic

**Acceptance Criteria:**
- [ ] Permission resolution algorithm updated
- [ ] global_permissions table references removed
- [ ] Methods available for MenuRibbon UC
- [ ] Permission hierarchy logic implemented

### Task 3.3: Update Permission Models
**File:** `Modules/Models/PermissionManagementForm/PermissionModels.cs`
**Status:** ❌ TODO
**Priority:** MEDIUM
**Dependencies:** Task 1.1

**Requirements:**
- Update models for users table structure
- Add UserPermOverrides property
- Add global permission properties (new_perm, edit_perm, delete_perm, print_perm)
- Remove global_permissions table references

**Acceptance Criteria:**
- [ ] Models updated for users table structure
- [ ] UserPermOverrides property added
- [ ] Global permission properties added
- [ ] global_permissions table references removed

---

## Phase 4: Form Integration ⏳

### Task 4.1: MenuRibbon UC Integration Pattern
**Files:** All Forms
**Status:** ❌ TODO
**Priority:** HIGH
**Dependencies:** Task 3.1

**Requirements:**
- Configure MenuRibbon UC with FormName and CurrentUserId
- Set form state properties (IsEditMode, HasUnsavedChanges, etc.)
- Wire up button events to form logic
- Call RefreshAll() to update button states based on final permissions

**Acceptance Criteria:**
- [ ] MenuRibbon UC configured in all forms
- [ ] Form state properties set correctly
- [ ] Button events wired up
- [ ] RefreshAll() called appropriately

---

## Phase 5: SQL Procedures Update ⏳

### Task 5.1: Update Core Permission Procedures
**Files:** 
- `Modules/Procedures/Permissions/Permission-Inheritance-Logic.sql`
- `Modules/Procedures/Permissions/Permission-Queries.sql`
- All user-related procedures in permissions folder

**Status:** ❌ TODO
**Priority:** HIGH
**Dependencies:** Task 1.1

**Requirements:**
- Update queries to use users table fields instead of global_permissions table
- Modify permission resolution logic to respect user_perm_overrides flag
- Remove global_permissions table references
- Add new permission resolution function

**Acceptance Criteria:**
- [ ] Queries updated for users table fields
- [ ] Override flag logic in procedures
- [ ] global_permissions table references removed
- [ ] Permission resolution function implemented

---

## Phase 6: Architecture Document Correction ⏳

### Task 6.1: Update Global Permission System Architecture
**File:** `docs/Global-Permission-System-Architecture.md`
**Status:** ❌ TODO
**Priority:** MEDIUM
**Dependencies:** All previous tasks

**Requirements:**
- Correct database schema to reflect users table structure
- Remove global_permissions table references
- Update permission resolution logic documentation
- Document MenuRibbon UC integration pattern
- Remove real-time update requirements

**Acceptance Criteria:**
- [ ] Database schema corrected
- [ ] global_permissions table references removed
- [ ] Permission resolution logic documented
- [ ] MenuRibbon UC integration documented
- [ ] Real-time update requirements removed

---

## Implementation Status Summary

**Total Tasks:** 8
**Completed:** 0 ✅
**In Progress:** 0 ⏳
**TODO:** 8 ❌

**Critical Path:**
1. Task 1.1 (Database Service) → Task 3.1 (MenuRibbon UC) → Task 4.1 (Form Integration)

**Key Dependencies:**
- All tasks depend on Task 1.1 (Database Service Updates)
- Form integration depends on MenuRibbon UC implementation
- Documentation updates should be done last

**Next Immediate Actions:**
1. Start with Task 1.1: Update PermissionDatabaseService.cs
2. Identify User Entry form for Task 2.1
3. Implement HasEffectivePermission method in Task 3.1

## Success Criteria Checklist

- [ ] MenuRibbon UC final permission calculation works correctly
- [ ] Override controls function correctly in both forms
- [ ] Permission hierarchy works with proper precedence using users table
- [ ] User permissions editable only when override enabled
- [ ] MenuRibbon UC algorithm works correctly (form integration handled manually)
- [ ] New/Edit/Delete/Print buttons respond correctly to final calculated permissions
- [ ] Architecture document reflects actual database structure and MenuRibbon UC integration
