using System;
using System.Collections.Generic;
using System.Diagnostics;
using ProManage.Modules.Services;
using ProManage.Modules.Models;

namespace ProManage.Tests
{
    /// <summary>
    /// Manual test class to verify Form Discovery Service implementation
    /// Run this to test the first 2 tasks implementation
    /// </summary>
    public static class ManualFormDiscoveryTest
    {
        /// <summary>
        /// Run all manual tests for Form Discovery Service
        /// </summary>
        public static void RunAllTests()
        {
            Debug.WriteLine("=== Starting Manual Form Discovery Service Tests ===");
            
            try
            {
                TestFormNameNormalization();
                TestFormFileValidation();
                TestFormNameFiltering();
                TestCacheModels();
                TestProgressModels();
                TestSyncConfiguration();
                TestExceptionModels();
                TestLoggingService();
                TestCacheService();
                TestMutexService();
                
                Debug.WriteLine("=== All Manual Tests Completed Successfully ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"=== Manual Test Failed: {ex.Message} ===");
            }
        }

        private static void TestFormNameNormalization()
        {
            Debug.WriteLine("Testing Form Name Normalization...");
            
            var testCases = new Dictionary<string, string>
            {
                { "testForm", "TESTFORM" },
                { "DatabaseForm", "DATABASEFORM" },
                { "permissionManagementForm", "PERMISSIONMANAGEMENTFORM" }
            };

            foreach (var testCase in testCases)
            {
                var result = FormDiscoveryService.NormalizeFormName(testCase.Key);
                Debug.WriteLine($"  {testCase.Key} -> {result} (Expected: {testCase.Value})");
                
                if (result != testCase.Value)
                {
                    throw new Exception($"Normalization failed for {testCase.Key}");
                }
            }
            
            Debug.WriteLine("✓ Form Name Normalization tests passed");
        }

        private static void TestFormFileValidation()
        {
            Debug.WriteLine("Testing Form File Validation...");
            
            var testFiles = new Dictionary<string, bool>
            {
                { "MainForm.cs", true },
                { "MainForm.Designer.cs", false },
                { "TestForm.resx", false },
                { "DatabaseForm.cs", true },
                { "SomeFile.txt", false }
            };

            foreach (var testFile in testFiles)
            {
                var result = FormDiscoveryService.IsValidFormFile(testFile.Key);
                Debug.WriteLine($"  {testFile.Key} -> {result} (Expected: {testFile.Value})");
                
                if (result != testFile.Value)
                {
                    throw new Exception($"File validation failed for {testFile.Key}");
                }
            }
            
            Debug.WriteLine("✓ Form File Validation tests passed");
        }

        private static void TestFormNameFiltering()
        {
            Debug.WriteLine("Testing Form Name Filtering...");
            
            var formNames = new List<string> 
            { 
                "MainForm", 
                "TestForm", 
                "SampleTests", 
                "UnitTests",
                "DatabaseForm",
                "PermissionTests"
            };

            var result = FormDiscoveryService.FilterValidFormNames(formNames);
            
            Debug.WriteLine($"  Input: {string.Join(", ", formNames)}");
            Debug.WriteLine($"  Output: {string.Join(", ", result)}");
            
            // Should exclude files ending with "Tests"
            foreach (var form in result)
            {
                if (form.EndsWith("Tests", StringComparison.OrdinalIgnoreCase))
                {
                    throw new Exception($"Test file {form} was not filtered out");
                }
            }
            
            Debug.WriteLine("✓ Form Name Filtering tests passed");
        }

        private static void TestCacheModels()
        {
            Debug.WriteLine("Testing Cache Models...");
            
            var cache = new FormScanCache
            {
                LastScanTime = DateTime.Now.AddMinutes(-25),
                FormListHash = "test-hash",
                CachedFormList = new List<string> { "TestForm" }
            };

            Debug.WriteLine($"  Cache IsValid (25 min old): {cache.IsValid}");
            
            if (!cache.IsValid)
            {
                throw new Exception("Cache should be valid within 30-minute window");
            }

            cache.LastScanTime = DateTime.Now.AddMinutes(-35);
            Debug.WriteLine($"  Cache IsValid (35 min old): {cache.IsValid}");
            
            if (cache.IsValid)
            {
                throw new Exception("Cache should be invalid after 30 minutes");
            }
            
            Debug.WriteLine("✓ Cache Models tests passed");
        }

        private static void TestProgressModels()
        {
            Debug.WriteLine("Testing Progress Models...");
            
            var progress = new SyncProgress
            {
                TotalOperations = 100,
                CompletedOperations = 25
            };

            Debug.WriteLine($"  Progress: {progress.CompletedOperations}/{progress.TotalOperations} = {progress.PercentComplete}%");
            
            if (progress.PercentComplete != 25)
            {
                throw new Exception("Progress calculation is incorrect");
            }
            
            Debug.WriteLine("✓ Progress Models tests passed");
        }

        private static void TestSyncConfiguration()
        {
            Debug.WriteLine("Testing Sync Configuration...");
            
            var config = new SyncConfiguration();
            
            Debug.WriteLine($"  Transaction Timeout: {config.TransactionTimeout}");
            Debug.WriteLine($"  Cache Expiration: {config.CacheExpiration}");
            Debug.WriteLine($"  Enable Persistent Cache: {config.EnablePersistentCache}");
            
            if (config.TransactionTimeout != TimeSpan.FromMinutes(5))
            {
                throw new Exception("Default transaction timeout is incorrect");
            }
            
            Debug.WriteLine("✓ Sync Configuration tests passed");
        }

        private static void TestExceptionModels()
        {
            Debug.WriteLine("Testing Exception Models...");
            
            var exception = new FormSyncException("TestForm", SyncOperationType.AddForm, "Test error");
            
            Debug.WriteLine($"  Exception FormName: {exception.FormName}");
            Debug.WriteLine($"  Exception OperationType: {exception.OperationType}");
            Debug.WriteLine($"  Exception Message: {exception.Message}");
            
            if (exception.FormName != "TestForm" || exception.OperationType != SyncOperationType.AddForm)
            {
                throw new Exception("Exception properties not set correctly");
            }
            
            Debug.WriteLine("✓ Exception Models tests passed");
        }

        private static void TestLoggingService()
        {
            Debug.WriteLine("Testing Logging Service...");
            
            var isConfigured = SyncLoggingService.IsConfigured();
            Debug.WriteLine($"  Initial configuration state: {isConfigured}");
            
            SyncLoggingService.LogSyncStart(5);
            SyncLoggingService.LogSyncComplete(3, 2, TimeSpan.FromSeconds(10));
            
            Debug.WriteLine("✓ Logging Service tests passed");
        }

        private static void TestCacheService()
        {
            Debug.WriteLine("Testing Cache Service...");
            
            var forms = new List<string> { "FormA", "FormB", "FormC" };
            var hash1 = FormScanCacheService.GenerateFormListHash(forms);
            var hash2 = FormScanCacheService.GenerateFormListHash(new List<string> { "FormC", "FormA", "FormB" });
            
            Debug.WriteLine($"  Hash 1: {hash1}");
            Debug.WriteLine($"  Hash 2: {hash2}");
            
            if (hash1 != hash2)
            {
                throw new Exception("Hash should be same regardless of order");
            }
            
            Debug.WriteLine("✓ Cache Service tests passed");
        }

        private static void TestMutexService()
        {
            Debug.WriteLine("Testing Mutex Service...");
            
            var key1 = GlobalSyncMutexService.GenerateAdvisoryLockKey();
            var key2 = GlobalSyncMutexService.GenerateAdvisoryLockKey();
            
            Debug.WriteLine($"  Advisory Lock Key 1: {key1}");
            Debug.WriteLine($"  Advisory Lock Key 2: {key2}");
            
            if (key1 != key2)
            {
                throw new Exception("Advisory lock key should be consistent");
            }
            
            Debug.WriteLine("✓ Mutex Service tests passed");
        }
    }
}
