# Task 07: Permission Database Operations

## Objective
Extend the database service with additional CRUD operations for permission management, including batch updates, user permission overrides, and global permission management. This completes the database layer for the RBAC system.

## Priority
**CORE SERVICES** - Depends on Tasks 01-06

## Estimated Time
1.5 hours

## Dependencies
- Task 04: Database Connection Service for Permissions
- Task 06: Core Permission Service Logic

## Files to Modify/Create
- `Modules/Connections/PermissionDatabaseService.cs` (extend existing)
- `Modules/Procedures/Permissions/Permission-Batch-Operations.sql`

## Additional Database Operations

### Extend PermissionDatabaseService.cs

Add these methods to the existing PermissionDatabaseService class:

```csharp
#region User Permission Override Operations

/// <summary>
/// Update user permission overrides (batch operation)
/// </summary>
public bool UpdateUserPermissions(List<UserPermissionUpdate> updates)
{
    using (var connection = _connectionManager.GetConnection())
    {
        using (var transaction = connection.BeginTransaction())
        {
            try
            {
                foreach (var update in updates)
                {
                    // Check if user permission record exists
                    var existingPermission = GetUserPermission(update.UserId, update.FormName);
                    
                    if (existingPermission != null)
                    {
                        // Update existing record
                        UpdateUserPermissionRecord(update, connection, transaction);
                    }
                    else
                    {
                        // Insert new record
                        InsertUserPermissionRecord(update, connection, transaction);
                    }
                }
                
                transaction.Commit();
                return true;
            }
            catch
            {
                transaction.Rollback();
                return false;
            }
        }
    }
}

/// <summary>
/// Update existing user permission record
/// </summary>
private void UpdateUserPermissionRecord(UserPermissionUpdate update, SqlConnection connection, SqlTransaction transaction)
{
    const string query = @"
        UPDATE user_permissions 
        SET read_permission = @readPermission,
            new_permission = @newPermission,
            edit_permission = @editPermission,
            delete_permission = @deletePermission,
            print_permission = @printPermission
        WHERE user_id = @userId AND form_name = @formName";
    
    using (var command = new SqlCommand(query, connection, transaction))
    {
        command.Parameters.AddWithValue("@userId", update.UserId);
        command.Parameters.AddWithValue("@formName", update.FormName);
        command.Parameters.AddWithValue("@readPermission", (object)update.ReadPermission ?? DBNull.Value);
        command.Parameters.AddWithValue("@newPermission", (object)update.NewPermission ?? DBNull.Value);
        command.Parameters.AddWithValue("@editPermission", (object)update.EditPermission ?? DBNull.Value);
        command.Parameters.AddWithValue("@deletePermission", (object)update.DeletePermission ?? DBNull.Value);
        command.Parameters.AddWithValue("@printPermission", (object)update.PrintPermission ?? DBNull.Value);
        
        command.ExecuteNonQuery();
    }
}

/// <summary>
/// Insert new user permission record
/// </summary>
private void InsertUserPermissionRecord(UserPermissionUpdate update, SqlConnection connection, SqlTransaction transaction)
{
    const string query = @"
        INSERT INTO user_permissions (user_id, form_name, read_permission, new_permission, edit_permission, delete_permission, print_permission)
        VALUES (@userId, @formName, @readPermission, @newPermission, @editPermission, @deletePermission, @printPermission)";
    
    using (var command = new SqlCommand(query, connection, transaction))
    {
        command.Parameters.AddWithValue("@userId", update.UserId);
        command.Parameters.AddWithValue("@formName", update.FormName);
        command.Parameters.AddWithValue("@readPermission", (object)update.ReadPermission ?? DBNull.Value);
        command.Parameters.AddWithValue("@newPermission", (object)update.NewPermission ?? DBNull.Value);
        command.Parameters.AddWithValue("@editPermission", (object)update.EditPermission ?? DBNull.Value);
        command.Parameters.AddWithValue("@deletePermission", (object)update.DeletePermission ?? DBNull.Value);
        command.Parameters.AddWithValue("@printPermission", (object)update.PrintPermission ?? DBNull.Value);
        
        command.ExecuteNonQuery();
    }
}

/// <summary>
/// Remove user permission override (revert to role permission)
/// </summary>
public bool RemoveUserPermissionOverride(int userId, string formName)
{
    const string query = "DELETE FROM user_permissions WHERE user_id = @userId AND form_name = @formName";
    
    using (var connection = _connectionManager.GetConnection())
    {
        using (var command = new SqlCommand(query, connection))
        {
            command.Parameters.AddWithValue("@userId", userId);
            command.Parameters.AddWithValue("@formName", formName);
            
            return command.ExecuteNonQuery() > 0;
        }
    }
}

/// <summary>
/// Remove all user permission overrides for a user
/// </summary>
public bool RemoveAllUserPermissionOverrides(int userId)
{
    const string query = "DELETE FROM user_permissions WHERE user_id = @userId";
    
    using (var connection = _connectionManager.GetConnection())
    {
        using (var command = new SqlCommand(query, connection))
        {
            command.Parameters.AddWithValue("@userId", userId);
            return command.ExecuteNonQuery() >= 0; // 0 or more rows affected is success
        }
    }
}

#endregion

#region Global Permission Operations

/// <summary>
/// Update global permissions for user
/// </summary>
public bool UpdateGlobalPermissions(GlobalPermissionUpdate update)
{
    using (var connection = _connectionManager.GetConnection())
    {
        using (var transaction = connection.BeginTransaction())
        {
            try
            {
                // Check if global permission record exists
                var existingPermission = GetGlobalPermissions(update.UserId);
                
                if (existingPermission != null)
                {
                    // Update existing record
                    const string updateQuery = @"
                        UPDATE global_permissions 
                        SET can_create_users = @canCreateUsers,
                            can_edit_users = @canEditUsers,
                            can_delete_users = @canDeleteUsers,
                            can_print_users = @canPrintUsers
                        WHERE user_id = @userId";
                    
                    using (var command = new SqlCommand(updateQuery, connection, transaction))
                    {
                        command.Parameters.AddWithValue("@userId", update.UserId);
                        command.Parameters.AddWithValue("@canCreateUsers", update.CanCreateUsers);
                        command.Parameters.AddWithValue("@canEditUsers", update.CanEditUsers);
                        command.Parameters.AddWithValue("@canDeleteUsers", update.CanDeleteUsers);
                        command.Parameters.AddWithValue("@canPrintUsers", update.CanPrintUsers);
                        
                        command.ExecuteNonQuery();
                    }
                }
                else
                {
                    // Insert new record
                    const string insertQuery = @"
                        INSERT INTO global_permissions (user_id, can_create_users, can_edit_users, can_delete_users, can_print_users)
                        VALUES (@userId, @canCreateUsers, @canEditUsers, @canDeleteUsers, @canPrintUsers)";
                    
                    using (var command = new SqlCommand(insertQuery, connection, transaction))
                    {
                        command.Parameters.AddWithValue("@userId", update.UserId);
                        command.Parameters.AddWithValue("@canCreateUsers", update.CanCreateUsers);
                        command.Parameters.AddWithValue("@canEditUsers", update.CanEditUsers);
                        command.Parameters.AddWithValue("@canDeleteUsers", update.CanDeleteUsers);
                        command.Parameters.AddWithValue("@canPrintUsers", update.CanPrintUsers);
                        
                        command.ExecuteNonQuery();
                    }
                }
                
                transaction.Commit();
                return true;
            }
            catch
            {
                transaction.Rollback();
                return false;
            }
        }
    }
}

/// <summary>
/// Remove global permissions for user
/// </summary>
public bool RemoveGlobalPermissions(int userId)
{
    const string query = "DELETE FROM global_permissions WHERE user_id = @userId";
    
    using (var connection = _connectionManager.GetConnection())
    {
        using (var command = new SqlCommand(query, connection))
        {
            command.Parameters.AddWithValue("@userId", userId);
            return command.ExecuteNonQuery() >= 0;
        }
    }
}

#endregion

#region Bulk Operations

/// <summary>
/// Get all users with their effective permissions
/// </summary>
public List<UserWithPermissions> GetAllUsersWithPermissions()
{
    const string query = @"
        SELECT u.user_id, u.username, u.full_name, u.role_id, r.role_name
        FROM users u
        LEFT JOIN roles r ON u.role_id = r.role_id
        WHERE u.is_active = 1
        ORDER BY u.username";
    
    var users = new List<UserWithPermissions>();
    
    using (var connection = _connectionManager.GetConnection())
    {
        using (var command = new SqlCommand(query, connection))
        {
            using (var reader = command.ExecuteReader())
            {
                while (reader.Read())
                {
                    var user = new UserWithPermissions
                    {
                        UserId = reader.GetInt32("user_id"),
                        Username = reader.GetString("username"),
                        FullName = reader.IsDBNull("full_name") ? null : reader.GetString("full_name"),
                        RoleId = reader.IsDBNull("role_id") ? 0 : reader.GetInt32("role_id"),
                        RoleName = reader.IsDBNull("role_name") ? null : reader.GetString("role_name")
                    };
                    
                    users.Add(user);
                }
            }
        }
        
        // Load permissions for each user
        foreach (var user in users)
        {
            user.FormPermissions = GetEffectivePermissionsForUser(user.UserId, user.RoleId);
            user.GlobalPermissions = GetGlobalPermissions(user.UserId);
        }
    }
    
    return users;
}

/// <summary>
/// Get effective permissions for a specific user
/// </summary>
private List<EffectivePermission> GetEffectivePermissionsForUser(int userId, int roleId)
{
    var effectivePermissions = new List<EffectivePermission>();
    
    // Get all forms from role permissions
    var rolePermissions = GetRolePermissions(roleId);
    var userPermissions = GetUserPermissions(userId);
    
    foreach (var rolePermission in rolePermissions)
    {
        var userOverride = userPermissions.FirstOrDefault(up => up.FormName == rolePermission.FormName);
        
        var effective = new EffectivePermission
        {
            FormName = rolePermission.FormName,
            ReadPermission = userOverride?.ReadPermission ?? rolePermission.ReadPermission,
            NewPermission = userOverride?.NewPermission ?? rolePermission.NewPermission,
            EditPermission = userOverride?.EditPermission ?? rolePermission.EditPermission,
            DeletePermission = userOverride?.DeletePermission ?? rolePermission.DeletePermission,
            PrintPermission = userOverride?.PrintPermission ?? rolePermission.PrintPermission,
            Source = userOverride != null ? PermissionSource.UserOverride : PermissionSource.Role
        };
        
        effectivePermissions.Add(effective);
    }
    
    return effectivePermissions;
}

/// <summary>
/// Copy permissions from one role to another
/// </summary>
public bool CopyRolePermissions(int sourceRoleId, int targetRoleId)
{
    using (var connection = _connectionManager.GetConnection())
    {
        using (var transaction = connection.BeginTransaction())
        {
            try
            {
                // First, delete existing permissions for target role
                const string deleteQuery = "DELETE FROM role_permissions WHERE role_id = @targetRoleId";
                using (var deleteCommand = new SqlCommand(deleteQuery, connection, transaction))
                {
                    deleteCommand.Parameters.AddWithValue("@targetRoleId", targetRoleId);
                    deleteCommand.ExecuteNonQuery();
                }
                
                // Copy permissions from source role
                const string copyQuery = @"
                    INSERT INTO role_permissions (role_id, form_name, read_permission, new_permission, edit_permission, delete_permission, print_permission)
                    SELECT @targetRoleId, form_name, read_permission, new_permission, edit_permission, delete_permission, print_permission
                    FROM role_permissions
                    WHERE role_id = @sourceRoleId";
                
                using (var copyCommand = new SqlCommand(copyQuery, connection, transaction))
                {
                    copyCommand.Parameters.AddWithValue("@sourceRoleId", sourceRoleId);
                    copyCommand.Parameters.AddWithValue("@targetRoleId", targetRoleId);
                    copyCommand.ExecuteNonQuery();
                }
                
                transaction.Commit();
                return true;
            }
            catch
            {
                transaction.Rollback();
                return false;
            }
        }
    }
}

/// <summary>
/// Reset user to role permissions (remove all overrides)
/// </summary>
public bool ResetUserToRolePermissions(int userId)
{
    return RemoveAllUserPermissionOverrides(userId);
}

#endregion

#region Reporting and Analytics

/// <summary>
/// Get permission summary for reporting
/// </summary>
public PermissionSummary GetPermissionSummary()
{
    const string query = @"
        SELECT 
            (SELECT COUNT(*) FROM roles WHERE is_active = 1) as ActiveRoles,
            (SELECT COUNT(*) FROM users WHERE is_active = 1) as ActiveUsers,
            (SELECT COUNT(DISTINCT form_name) FROM role_permissions) as FormsInSystem,
            (SELECT COUNT(*) FROM user_permissions) as UserOverrides,
            (SELECT COUNT(*) FROM global_permissions) as UsersWithGlobalPermissions";
    
    using (var connection = _connectionManager.GetConnection())
    {
        using (var command = new SqlCommand(query, connection))
        {
            using (var reader = command.ExecuteReader())
            {
                if (reader.Read())
                {
                    return new PermissionSummary
                    {
                        ActiveRoles = reader.GetInt32("ActiveRoles"),
                        ActiveUsers = reader.GetInt32("ActiveUsers"),
                        FormsInSystem = reader.GetInt32("FormsInSystem"),
                        UserOverrides = reader.GetInt32("UserOverrides"),
                        UsersWithGlobalPermissions = reader.GetInt32("UsersWithGlobalPermissions")
                    };
                }
            }
        }
    }
    
    return new PermissionSummary();
}

/// <summary>
/// Get users without any permissions
/// </summary>
public List<int> GetUsersWithoutPermissions()
{
    const string query = @"
        SELECT u.user_id
        FROM users u
        LEFT JOIN roles r ON u.role_id = r.role_id
        WHERE u.is_active = 1 
        AND (u.role_id IS NULL OR r.is_active = 0)";
    
    var userIds = new List<int>();
    
    using (var connection = _connectionManager.GetConnection())
    {
        using (var command = new SqlCommand(query, connection))
        {
            using (var reader = command.ExecuteReader())
            {
                while (reader.Read())
                {
                    userIds.Add(reader.GetInt32("user_id"));
                }
            }
        }
    }
    
    return userIds;
}

#endregion
```

### Additional Models

Add to PermissionModels.cs:

```csharp
public class PermissionSummary
{
    public int ActiveRoles { get; set; }
    public int ActiveUsers { get; set; }
    public int FormsInSystem { get; set; }
    public int UserOverrides { get; set; }
    public int UsersWithGlobalPermissions { get; set; }
}
```

## SQL Procedures File

### Permission-Batch-Operations.sql
```sql
-- Batch update role permissions
CREATE PROCEDURE sp_UpdateRolePermissionsBatch
    @RoleId INT,
    @PermissionsXML XML
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRANSACTION;
    
    BEGIN TRY
        -- Parse XML and update permissions
        UPDATE rp
        SET read_permission = x.ReadPermission,
            new_permission = x.NewPermission,
            edit_permission = x.EditPermission,
            delete_permission = x.DeletePermission,
            print_permission = x.PrintPermission
        FROM role_permissions rp
        INNER JOIN (
            SELECT 
                T.c.value('@FormName', 'NVARCHAR(100)') AS FormName,
                T.c.value('@ReadPermission', 'BIT') AS ReadPermission,
                T.c.value('@NewPermission', 'BIT') AS NewPermission,
                T.c.value('@EditPermission', 'BIT') AS EditPermission,
                T.c.value('@DeletePermission', 'BIT') AS DeletePermission,
                T.c.value('@PrintPermission', 'BIT') AS PrintPermission
            FROM @PermissionsXML.nodes('/Permissions/Permission') T(c)
        ) x ON rp.form_name = x.FormName
        WHERE rp.role_id = @RoleId;
        
        COMMIT TRANSACTION;
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        THROW;
    END CATCH
END
```

## Acceptance Criteria

- [ ] User permission override CRUD operations implemented
- [ ] Global permission management operations
- [ ] Batch update operations for performance
- [ ] Permission copying between roles
- [ ] User permission reset functionality
- [ ] Reporting and analytics queries
- [ ] Proper transaction handling for all operations
- [ ] NULL handling for user permission overrides
- [ ] Performance optimized bulk operations

## Dependencies
- Task 04: Database Connection Service for Permissions
- Task 06: Core Permission Service Logic

## Next Tasks
This task enables:
- Task 08: Permission Caching and Performance
- Task 09: Permission Management Form (3-Tab UI)
- Task 11: User Master Form Permission Integration
