# Task 12: Permission Display Components

## Objective
Create reusable UI components for displaying permissions throughout the application. These components will provide consistent permission visualization, status indicators, and user-friendly permission summaries.

## Priority
**UI MANAGEMENT** - Depends on Tasks 01-11

## Estimated Time
1 hour

## Dependencies
- Task 02: Permission Data Models Creation
- Task 06: Core Permission Service Logic
- Task 09: Permission Management Form (3-Tab UI)

## Files to Create
- `Modules/UI/PermissionDisplayControl.cs`
- `Modules/UI/PermissionDisplayControl.Designer.cs`
- `Modules/UI/PermissionStatusIndicator.cs`
- `Modules/UI/PermissionSummaryPanel.cs`

## Component Overview

### 1. PermissionDisplayControl
A reusable control for displaying user permissions in a grid format with color coding.

### 2. PermissionStatusIndicator
A small indicator showing permission status (granted/denied/inherited).

### 3. PermissionSummaryPanel
A panel showing permission summary statistics and quick access buttons.

## Implementation

### PermissionDisplayControl.cs
```csharp
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Columns;
using ProManage.Modules.Models;
using ProManage.Modules.Services;

namespace ProManage.Modules.UI
{
    public partial class PermissionDisplayControl : UserControl
    {
        private GridControl gridControl;
        private GridView gridView;
        private List<EffectivePermission> _permissions;
        
        public event EventHandler<PermissionChangedEventArgs> PermissionChanged;
        
        public bool ReadOnly { get; set; } = true;
        public bool ShowSource { get; set; } = true;
        public bool ShowDisplayNames { get; set; } = true;
        
        public PermissionDisplayControl()
        {
            InitializeComponent();
            SetupGrid();
        }
        
        #region Grid Setup
        
        private void SetupGrid()
        {
            gridControl = new GridControl();
            gridView = new GridView();
            
            gridControl.MainView = gridView;
            gridControl.Dock = DockStyle.Fill;
            
            // Grid settings
            gridView.OptionsView.ShowGroupPanel = false;
            gridView.OptionsView.ShowFilterPanelMode = DevExpress.XtraGrid.Views.Base.ShowFilterPanelMode.Never;
            gridView.OptionsSelection.EnableAppearanceFocusedCell = false;
            gridView.OptionsCustomization.AllowColumnMoving = false;
            
            // Setup columns
            SetupColumns();
            
            // Events
            gridView.CellValueChanged += GridView_CellValueChanged;
            gridView.RowCellStyle += GridView_RowCellStyle;
            
            this.Controls.Add(gridControl);
        }
        
        private void SetupColumns()
        {
            gridView.Columns.Clear();
            
            // Form Name
            var colFormName = new GridColumn
            {
                FieldName = "FormName",
                Caption = "Form",
                Width = ShowDisplayNames ? 150 : 120,
                OptionsColumn = { AllowEdit = false }
            };
            gridView.Columns.Add(colFormName);
            
            // Display Name (if enabled)
            if (ShowDisplayNames)
            {
                var colDisplayName = new GridColumn
                {
                    FieldName = "DisplayName",
                    Caption = "Display Name",
                    Width = 180,
                    OptionsColumn = { AllowEdit = false }
                };
                gridView.Columns.Add(colDisplayName);
            }
            
            // Permission columns
            var permissions = new[]
            {
                new { Field = "ReadPermission", Caption = "Read", Width = 60 },
                new { Field = "NewPermission", Caption = "New", Width = 60 },
                new { Field = "EditPermission", Caption = "Edit", Width = 60 },
                new { Field = "DeletePermission", Caption = "Delete", Width = 60 },
                new { Field = "PrintPermission", Caption = "Print", Width = 60 }
            };
            
            foreach (var perm in permissions)
            {
                var col = new GridColumn
                {
                    FieldName = perm.Field,
                    Caption = perm.Caption,
                    Width = perm.Width,
                    UnboundType = DevExpress.Data.UnboundColumnType.Boolean,
                    OptionsColumn = { AllowEdit = !ReadOnly }
                };
                gridView.Columns.Add(col);
            }
            
            // Source column (if enabled)
            if (ShowSource)
            {
                var colSource = new GridColumn
                {
                    FieldName = "Source",
                    Caption = "Source",
                    Width = 80,
                    OptionsColumn = { AllowEdit = false }
                };
                gridView.Columns.Add(colSource);
            }
        }
        
        #endregion
        
        #region Data Binding
        
        public void LoadPermissions(List<EffectivePermission> permissions)
        {
            _permissions = permissions ?? new List<EffectivePermission>();
            
            var displayData = _permissions.Select(p => new PermissionDisplayItem
            {
                FormName = p.FormName,
                DisplayName = FormsConfigurationService.GetFormDisplayName(p.FormName),
                ReadPermission = p.ReadPermission,
                NewPermission = p.NewPermission,
                EditPermission = p.EditPermission,
                DeletePermission = p.DeletePermission,
                PrintPermission = p.PrintPermission,
                Source = p.Source.ToString()
            }).ToList();
            
            gridControl.DataSource = displayData;
            gridView.BestFitColumns();
        }
        
        public void LoadUserPermissions(int userId)
        {
            try
            {
                var permissions = PermissionService.GetUserEffectivePermissions(userId);
                LoadPermissions(permissions);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading permissions: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        public List<EffectivePermission> GetPermissions()
        {
            var displayData = (List<PermissionDisplayItem>)gridControl.DataSource;
            if (displayData == null) return new List<EffectivePermission>();
            
            return displayData.Select(d => new EffectivePermission
            {
                FormName = d.FormName,
                ReadPermission = d.ReadPermission,
                NewPermission = d.NewPermission,
                EditPermission = d.EditPermission,
                DeletePermission = d.DeletePermission,
                PrintPermission = d.PrintPermission,
                Source = Enum.Parse<PermissionSource>(d.Source)
            }).ToList();
        }
        
        #endregion
        
        #region Grid Events
        
        private void GridView_CellValueChanged(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            if (ReadOnly) return;
            
            var formName = gridView.GetRowCellValue(e.RowHandle, "FormName")?.ToString();
            var permissionType = GetPermissionTypeFromColumn(e.Column.FieldName);
            var newValue = (bool)e.Value;
            
            PermissionChanged?.Invoke(this, new PermissionChangedEventArgs
            {
                FormName = formName,
                PermissionType = permissionType,
                NewValue = newValue
            });
        }
        
        private void GridView_RowCellStyle(object sender, RowCellStyleEventArgs e)
        {
            if (!ShowSource || e.Column.FieldName == "Source" || e.Column.FieldName == "FormName" || e.Column.FieldName == "DisplayName")
                return;
            
            var source = gridView.GetRowCellValue(e.RowHandle, "Source")?.ToString();
            
            switch (source)
            {
                case "UserOverride":
                    e.Appearance.BackColor = Color.LightBlue;
                    e.Appearance.ForeColor = Color.DarkBlue;
                    break;
                case "Role":
                    e.Appearance.BackColor = Color.LightGreen;
                    e.Appearance.ForeColor = Color.DarkGreen;
                    break;
            }
        }
        
        private PermissionType GetPermissionTypeFromColumn(string fieldName)
        {
            return fieldName switch
            {
                "ReadPermission" => PermissionType.Read,
                "NewPermission" => PermissionType.New,
                "EditPermission" => PermissionType.Edit,
                "DeletePermission" => PermissionType.Delete,
                "PrintPermission" => PermissionType.Print,
                _ => PermissionType.Read
            };
        }
        
        #endregion
        
        #region Public Methods
        
        public void RefreshDisplay()
        {
            gridView.RefreshData();
            gridView.BestFitColumns();
        }
        
        public void SetColumnVisibility(string columnName, bool visible)
        {
            var column = gridView.Columns[columnName];
            if (column != null)
            {
                column.Visible = visible;
            }
        }
        
        public void ApplyFilter(string formNameFilter)
        {
            if (string.IsNullOrEmpty(formNameFilter))
            {
                gridView.ClearColumnsFilter();
            }
            else
            {
                gridView.SetRowCellValue(GridControl.AutoFilterRowHandle, "FormName", formNameFilter);
            }
        }
        
        #endregion
    }
    
    public class PermissionDisplayItem
    {
        public string FormName { get; set; }
        public string DisplayName { get; set; }
        public bool ReadPermission { get; set; }
        public bool NewPermission { get; set; }
        public bool EditPermission { get; set; }
        public bool DeletePermission { get; set; }
        public bool PrintPermission { get; set; }
        public string Source { get; set; }
    }
    
    public class PermissionChangedEventArgs : EventArgs
    {
        public string FormName { get; set; }
        public PermissionType PermissionType { get; set; }
        public bool NewValue { get; set; }
    }
}
```

### PermissionStatusIndicator.cs
```csharp
using System;
using System.Drawing;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using ProManage.Modules.Models;
using ProManage.Modules.Services;

namespace ProManage.Modules.UI
{
    public class PermissionStatusIndicator : UserControl
    {
        private PictureEdit pictureStatus;
        private LabelControl lblStatus;
        private LabelControl lblDescription;
        
        public PermissionStatus Status { get; private set; }
        public string Description { get; set; }
        
        public PermissionStatusIndicator()
        {
            InitializeComponent();
            SetStatus(PermissionStatus.Unknown);
        }
        
        private void InitializeComponent()
        {
            this.Size = new Size(120, 60);
            
            pictureStatus = new PictureEdit();
            lblStatus = new LabelControl();
            lblDescription = new LabelControl();
            
            // Picture
            pictureStatus.Location = new Point(5, 5);
            pictureStatus.Size = new Size(24, 24);
            pictureStatus.Properties.ShowCameraMenuItem = DevExpress.XtraEditors.Controls.CameraMenuItemVisibility.Never;
            pictureStatus.Properties.SizeMode = DevExpress.XtraEditors.Controls.PictureSizeMode.Zoom;
            
            // Status label
            lblStatus.Location = new Point(35, 5);
            lblStatus.Font = new Font(lblStatus.Font, FontStyle.Bold);
            
            // Description label
            lblDescription.Location = new Point(35, 25);
            lblDescription.Font = new Font(lblDescription.Font.FontFamily, 8);
            lblDescription.ForeColor = Color.Gray;
            
            this.Controls.AddRange(new Control[] { pictureStatus, lblStatus, lblDescription });
        }
        
        public void SetStatus(PermissionStatus status, string description = null)
        {
            Status = status;
            Description = description ?? GetDefaultDescription(status);
            
            UpdateDisplay();
        }
        
        public void CheckPermission(int userId, string formName, PermissionType permissionType)
        {
            try
            {
                var hasPermission = PermissionService.HasPermission(userId, formName, permissionType);
                var status = hasPermission ? PermissionStatus.Granted : PermissionStatus.Denied;
                var description = $"{permissionType} permission for {formName}";
                
                SetStatus(status, description);
            }
            catch
            {
                SetStatus(PermissionStatus.Error, "Error checking permission");
            }
        }
        
        private void UpdateDisplay()
        {
            switch (Status)
            {
                case PermissionStatus.Granted:
                    pictureStatus.Image = CreateStatusIcon(Color.Green, "✓");
                    lblStatus.Text = "Granted";
                    lblStatus.ForeColor = Color.Green;
                    break;
                    
                case PermissionStatus.Denied:
                    pictureStatus.Image = CreateStatusIcon(Color.Red, "✗");
                    lblStatus.Text = "Denied";
                    lblStatus.ForeColor = Color.Red;
                    break;
                    
                case PermissionStatus.Inherited:
                    pictureStatus.Image = CreateStatusIcon(Color.Blue, "↑");
                    lblStatus.Text = "Inherited";
                    lblStatus.ForeColor = Color.Blue;
                    break;
                    
                case PermissionStatus.Override:
                    pictureStatus.Image = CreateStatusIcon(Color.Orange, "⚡");
                    lblStatus.Text = "Override";
                    lblStatus.ForeColor = Color.Orange;
                    break;
                    
                case PermissionStatus.Error:
                    pictureStatus.Image = CreateStatusIcon(Color.Gray, "?");
                    lblStatus.Text = "Error";
                    lblStatus.ForeColor = Color.Gray;
                    break;
                    
                default:
                    pictureStatus.Image = CreateStatusIcon(Color.Gray, "?");
                    lblStatus.Text = "Unknown";
                    lblStatus.ForeColor = Color.Gray;
                    break;
            }
            
            lblDescription.Text = Description;
        }
        
        private Image CreateStatusIcon(Color color, string symbol)
        {
            var bitmap = new Bitmap(16, 16);
            using (var g = Graphics.FromImage(bitmap))
            {
                g.Clear(Color.Transparent);
                using (var brush = new SolidBrush(color))
                using (var font = new Font("Arial", 10, FontStyle.Bold))
                {
                    var size = g.MeasureString(symbol, font);
                    var x = (16 - size.Width) / 2;
                    var y = (16 - size.Height) / 2;
                    g.DrawString(symbol, font, brush, x, y);
                }
            }
            return bitmap;
        }
        
        private string GetDefaultDescription(PermissionStatus status)
        {
            return status switch
            {
                PermissionStatus.Granted => "Permission is granted",
                PermissionStatus.Denied => "Permission is denied",
                PermissionStatus.Inherited => "Permission inherited from role",
                PermissionStatus.Override => "Permission overridden by user",
                PermissionStatus.Error => "Error checking permission",
                _ => "Permission status unknown"
            };
        }
    }
    
    public enum PermissionStatus
    {
        Unknown,
        Granted,
        Denied,
        Inherited,
        Override,
        Error
    }
}
```

### PermissionSummaryPanel.cs
```csharp
using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using ProManage.Modules.Models;
using ProManage.Modules.Services;

namespace ProManage.Modules.UI
{
    public class PermissionSummaryPanel : GroupControl
    {
        private LabelControl lblTotalForms;
        private LabelControl lblAccessibleForms;
        private LabelControl lblOverrides;
        private LabelControl lblGlobalPermissions;
        private SimpleButton btnManagePermissions;
        private SimpleButton btnViewDetails;
        
        public event EventHandler ManagePermissionsClicked;
        public event EventHandler ViewDetailsClicked;
        
        public int UserId { get; set; }
        
        public PermissionSummaryPanel()
        {
            InitializeComponent();
        }
        
        private void InitializeComponent()
        {
            this.Text = "Permission Summary";
            this.Size = new Size(300, 150);
            
            // Labels
            lblTotalForms = new LabelControl { Location = new Point(10, 25), Text = "Total Forms: -" };
            lblAccessibleForms = new LabelControl { Location = new Point(10, 45), Text = "Accessible: -" };
            lblOverrides = new LabelControl { Location = new Point(10, 65), Text = "Overrides: -" };
            lblGlobalPermissions = new LabelControl { Location = new Point(10, 85), Text = "Global Permissions: -" };
            
            // Buttons
            btnManagePermissions = new SimpleButton
            {
                Location = new Point(10, 110),
                Size = new Size(100, 23),
                Text = "Manage"
            };
            
            btnViewDetails = new SimpleButton
            {
                Location = new Point(120, 110),
                Size = new Size(80, 23),
                Text = "Details"
            };
            
            // Events
            btnManagePermissions.Click += (s, e) => ManagePermissionsClicked?.Invoke(this, e);
            btnViewDetails.Click += (s, e) => ViewDetailsClicked?.Invoke(this, e);
            
            this.Controls.AddRange(new Control[]
            {
                lblTotalForms, lblAccessibleForms, lblOverrides, lblGlobalPermissions,
                btnManagePermissions, btnViewDetails
            });
        }
        
        public void LoadSummary(int userId)
        {
            UserId = userId;
            
            try
            {
                // Get user permissions
                var permissions = PermissionService.GetUserEffectivePermissions(userId);
                var totalForms = permissions.Count;
                var accessibleForms = permissions.Count(p => p.ReadPermission);
                var overrides = permissions.Count(p => p.Source == PermissionSource.UserOverride);
                
                // Get global permissions
                var globalPerms = new PermissionDatabaseService().GetGlobalPermissions(userId);
                var globalCount = 0;
                if (globalPerms != null)
                {
                    globalCount = new[] { globalPerms.CanCreateUsers, globalPerms.CanEditUsers, 
                                        globalPerms.CanDeleteUsers, globalPerms.CanPrintUsers }
                                        .Count(p => p);
                }
                
                // Update labels
                lblTotalForms.Text = $"Total Forms: {totalForms}";
                lblAccessibleForms.Text = $"Accessible: {accessibleForms}";
                lblAccessibleForms.ForeColor = accessibleForms > 0 ? Color.Green : Color.Red;
                
                lblOverrides.Text = $"Overrides: {overrides}";
                lblOverrides.ForeColor = overrides > 0 ? Color.Blue : Color.Black;
                
                lblGlobalPermissions.Text = $"Global Permissions: {globalCount}/4";
                lblGlobalPermissions.ForeColor = globalCount > 0 ? Color.Orange : Color.Black;
            }
            catch (Exception ex)
            {
                lblTotalForms.Text = "Error loading summary";
                lblTotalForms.ForeColor = Color.Red;
                System.Diagnostics.Debug.WriteLine($"Error loading permission summary: {ex.Message}");
            }
        }
        
        public void RefreshSummary()
        {
            if (UserId > 0)
            {
                LoadSummary(UserId);
            }
        }
    }
}
```

## Usage Examples

### In Forms
```csharp
// Add permission display to any form
var permissionDisplay = new PermissionDisplayControl
{
    ReadOnly = true,
    ShowSource = true,
    ShowDisplayNames = true
};
permissionDisplay.LoadUserPermissions(userId);

// Add status indicator
var statusIndicator = new PermissionStatusIndicator();
statusIndicator.CheckPermission(userId, "EstimateForm", PermissionType.Edit);

// Add summary panel
var summaryPanel = new PermissionSummaryPanel();
summaryPanel.LoadSummary(userId);
summaryPanel.ManagePermissionsClicked += (s, e) => OpenPermissionManagement();
```

## Acceptance Criteria

- [ ] Reusable permission display grid control
- [ ] Permission status indicator with visual feedback
- [ ] Permission summary panel with statistics
- [ ] Color coding for permission sources (role vs override)
- [ ] Event handling for permission changes
- [ ] Error handling and graceful degradation
- [ ] Consistent visual styling across components
- [ ] Easy integration into existing forms

## Dependencies
- Task 02: Permission Data Models Creation
- Task 06: Core Permission Service Logic
- Task 09: Permission Management Form (3-Tab UI)

## Next Tasks
This task enables:
- Task 13: MainFrame Ribbon Permission Filtering
- Task 14: Individual Form Permission Checks
- Task 16: Testing and Validation Suite
